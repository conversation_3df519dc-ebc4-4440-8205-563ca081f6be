/* CSS Custom Properties for Blue Theme */
:root {
    --primary-blue: #2563eb;
    --primary-blue-dark: #1d4ed8;
    --primary-blue-light: #3b82f6;
    --secondary-blue: #60a5fa;
    --accent-blue: #93c5fd;
    --light-blue: #dbeafe;
    --very-light-blue: #eff6ff;
    
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #06b6d4;
    
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --text-white: #ffffff;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --bg-dark: #111827;
    
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-family-arabic: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    
    --transition: all 0.3s ease;
    --transition-fast: all 0.15s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    transition: var(--transition);
}

/* Arabic RTL Support */
[dir="rtl"] {
    font-family: var(--font-family-arabic);
}

[dir="rtl"] .sidebar {
    right: 0;
    left: auto;
}

[dir="rtl"] .main-content {
    margin-right: 280px;
    margin-left: 0;
}

[dir="rtl"] .sidebar.collapsed + .main-content {
    margin-right: 70px;
}

/* Login Page Styles */
.login-page {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.login-container {
    width: 100%;
    max-width: 450px;
}

.login-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--text-white);
    padding: 2rem;
    text-align: center;
    position: relative;
}

.login-header .logo {
    margin-bottom: 1rem;
}

.login-header .logo i {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    display: block;
}

.login-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.language-toggle {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

[dir="rtl"] .language-toggle {
    right: auto;
    left: 1rem;
}

.btn-language {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--text-white);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.btn-language:hover {
    background: rgba(255, 255, 255, 0.3);
}

.btn-language i {
    margin-left: 0.5rem;
}

[dir="rtl"] .btn-language i {
    margin-left: 0;
    margin-right: 0.5rem;
}

.login-form {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    left: 1rem;
    color: var(--text-secondary);
    z-index: 2;
}

[dir="rtl"] .input-group i {
    left: auto;
    right: 1rem;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 3rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background-color: var(--bg-primary);
}

[dir="rtl"] .input-group input,
[dir="rtl"] .input-group select {
    padding: 0.875rem 3rem 0.875rem 1rem;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.toggle-password {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    z-index: 2;
}

[dir="rtl"] .toggle-password {
    right: auto;
    left: 1rem;
}

.btn-login {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
    color: var(--text-white);
    border: none;
    padding: 1rem;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 1rem;
}

.btn-login:hover {
    background: linear-gradient(135deg, var(--primary-blue-dark), var(--primary-blue));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.login-help {
    text-align: center;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    margin-top: 1rem;
}

.login-help p {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.login-help small {
    color: var(--text-secondary);
    line-height: 1.8;
}

.btn-auto-login {
    width: 100%;
    background: linear-gradient(135deg, var(--success-color), #34d399);
    color: var(--text-white);
    border: none;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.btn-auto-login:hover {
    background: linear-gradient(135deg, #34d399, var(--success-color));
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.error-message {
    background: var(--error-color);
    color: var(--text-white);
    padding: 1rem;
    margin: 1rem 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: var(--text-white);
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.loading-spinner p {
    font-size: 1.1rem;
    font-weight: 500;
}

/* Dashboard Page Styles */
.dashboard-page {
    background: var(--bg-secondary);
    min-height: 100vh;
}

/* Header Styles */
.main-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 70px;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 1.5rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: var(--bg-tertiary);
    color: var(--primary-blue);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--primary-blue);
}

.logo i {
    font-size: 1.5rem;
}

/* Search Box */
.search-container {
    flex: 1;
    max-width: 500px;
    margin: 0 2rem;
    position: relative;
}

.search-box {
    position: relative;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    z-index: 2;
}

[dir="rtl"] .search-box i {
    left: auto;
    right: 1rem;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--bg-secondary);
}

[dir="rtl"] .search-box input {
    padding: 0.75rem 3rem 0.75rem 1rem;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-blue);
    background: var(--bg-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1001;
    display: none;
}

/* Header Actions */
.header-right {
    display: flex;
    align-items: center;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-icon {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.btn-icon:hover {
    background: var(--bg-tertiary);
    color: var(--primary-blue);
}

/* User Menu */
.user-menu {
    position: relative;
    margin-left: 1rem;
}

[dir="rtl"] .user-menu {
    margin-left: 0;
    margin-right: 1rem;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    color: var(--text-primary);
}

.user-profile:hover {
    background: var(--bg-tertiary);
}

.user-profile i:first-child {
    font-size: 1.5rem;
    color: var(--primary-blue);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    z-index: 1001;
    display: none;
    overflow: hidden;
}

[dir="rtl"] .user-dropdown {
    right: auto;
    left: 0;
}

.user-dropdown a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1rem;
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition);
}

.user-dropdown a:hover {
    background: var(--bg-tertiary);
    color: var(--primary-blue);
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 70px;
    left: 0;
    width: 280px;
    height: calc(100vh - 70px);
    background: var(--bg-primary);
    border-right: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    z-index: 999;
    transition: var(--transition);
    overflow-y: auto;
}

[dir="rtl"] .sidebar {
    left: auto;
    right: 0;
    border-right: none;
    border-left: 1px solid var(--border-color);
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    background: var(--light-blue);
    color: var(--primary-blue);
}

.nav-link.active {
    background: var(--light-blue);
    color: var(--primary-blue);
    font-weight: 600;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-blue);
}

[dir="rtl"] .nav-link.active::before {
    left: auto;
    right: 0;
}

.nav-link i {
    font-size: 1.25rem;
    width: 20px;
    text-align: center;
}

.nav-badge {
    background: var(--primary-blue);
    color: var(--text-white);
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    margin-left: auto;
    min-width: 20px;
    text-align: center;
}

[dir="rtl"] .nav-badge {
    margin-left: 0;
    margin-right: auto;
}

.sidebar.collapsed .nav-link span,
.sidebar.collapsed .nav-badge {
    display: none;
}

/* Main Content */
.main-content {
    margin-left: 280px;
    margin-top: 70px;
    padding: 2rem;
    min-height: calc(100vh - 70px);
    transition: var(--transition);
}

[dir="rtl"] .main-content {
    margin-left: 0;
    margin-right: 280px;
}

.sidebar.collapsed + .main-content {
    margin-left: 70px;
}

[dir="rtl"] .sidebar.collapsed + .main-content {
    margin-left: 0;
    margin-right: 70px;
}

/* Module Styles */
.module {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.module.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.module-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-light);
}

.module-header h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.module-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.last-update {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--text-white);
}

.stat-icon.customers {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
}

.stat-icon.suppliers {
    background: linear-gradient(135deg, var(--success-color), #34d399);
}

.stat-icon.telesales {
    background: linear-gradient(135deg, var(--warning-color), #fbbf24);
}

.stat-icon.tasks {
    background: linear-gradient(135deg, var(--info-color), #22d3ee);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
}

.stat-content p {
    color: var(--text-secondary);
    margin: 0;
    font-weight: 500;
}

/* Dashboard Content */
.dashboard-content {
    margin-top: 2rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.dashboard-widget {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.dashboard-widget h3 {
    background: var(--bg-tertiary);
    padding: 1rem 1.5rem;
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
}

.widget-content {
    padding: 1.5rem;
    min-height: 200px;
}

/* Modal Overlay */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: none;
    align-items: center;
    justify-content: center;
}

/* Notification Container */
.notification-container {
    position: fixed;
    top: 90px;
    right: 1rem;
    z-index: 3000;
    max-width: 400px;
}

[dir="rtl"] .notification-container {
    right: auto;
    left: 1rem;
}

/* Export Modal Styles */
.export-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.export-modal.active {
    opacity: 1;
    visibility: visible;
}

.export-modal .modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.export-modal .modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    z-index: 1;
}

.export-modal .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.export-modal .modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.export-modal .modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.export-modal .modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.export-modal .modal-body {
    padding: 1.5rem;
}

.export-modal .modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.export-section {
    margin-bottom: 2rem;
}

.export-section h4 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.export-checkboxes {
    display: grid;
    gap: 0.75rem;
}

.export-checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.export-checkbox:hover {
    background: var(--bg-tertiary);
}

.export-checkbox input[type="checkbox"] {
    display: none;
}

.export-checkbox .checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: var(--transition);
}

.export-checkbox input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
}

.export-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.export-checkbox .count {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-left: auto;
}

[dir="rtl"] .export-checkbox .count {
    margin-left: 0;
    margin-right: auto;
}

.export-formats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.export-format {
    cursor: pointer;
}

.export-format input[type="radio"] {
    display: none;
}

.export-format .format-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.export-format input[type="radio"]:checked + .format-option {
    border-color: var(--primary-blue);
    background: var(--light-blue);
}

.export-format .format-option i {
    font-size: 1.5rem;
    color: var(--primary-blue);
}

.export-settings {
    display: grid;
    gap: 0.75rem;
}

.export-setting {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
}

.export-setting input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-blue);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
    color: var(--text-white);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-blue-dark), var(--primary-blue));
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--border-color);
    transform: translateY(-1px);
}

.btn-success {
    background: var(--success-color);
    color: var(--text-white);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-white);
}

.btn-danger {
    background: var(--error-color);
    color: var(--text-white);
}

/* Notification Styles */
.notification-container {
    position: fixed;
    top: 90px;
    right: 1rem;
    z-index: 3000;
    max-width: 400px;
}

[dir="rtl"] .notification-container {
    right: auto;
    left: 1rem;
}

.notification {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    margin-bottom: 0.5rem;
    overflow: hidden;
    transform: translateX(100%);
    opacity: 0;
    transition: var(--transition);
}

[dir="rtl"] .notification {
    transform: translateX(-100%);
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-success {
    border-left: 4px solid var(--success-color);
}

[dir="rtl"] .notification-success {
    border-left: none;
    border-right: 4px solid var(--success-color);
}

.notification-error {
    border-left: 4px solid var(--error-color);
}

[dir="rtl"] .notification-error {
    border-left: none;
    border-right: 4px solid var(--error-color);
}

.notification-warning {
    border-left: 4px solid var(--warning-color);
}

[dir="rtl"] .notification-warning {
    border-left: none;
    border-right: 4px solid var(--warning-color);
}

.notification-info {
    border-left: 4px solid var(--info-color);
}

[dir="rtl"] .notification-info {
    border-left: none;
    border-right: 4px solid var(--info-color);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
}

.notification-content i {
    font-size: 1.25rem;
}

.notification-success .notification-content i {
    color: var(--success-color);
}

.notification-error .notification-content i {
    color: var(--error-color);
}

.notification-warning .notification-content i {
    color: var(--warning-color);
}

.notification-info .notification-content i {
    color: var(--info-color);
}

.notification-close {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

[dir="rtl"] .notification-close {
    right: auto;
    left: 0.5rem;
}

.notification-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.5rem !important; }
.mb-2 { margin-bottom: 1rem !important; }
.mb-3 { margin-bottom: 1.5rem !important; }
.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.5rem !important; }
.mt-2 { margin-top: 1rem !important; }
.mt-3 { margin-top: 1.5rem !important; }

/* Customer Management Styles */
.customers-content {
    padding: 0;
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.filters-section {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.filters-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.search-input {
    position: relative;
}

.search-input i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    z-index: 2;
}

[dir="rtl"] .search-input i {
    left: auto;
    right: 1rem;
}

.search-input input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

[dir="rtl"] .search-input input {
    padding: 0.75rem 3rem 0.75rem 1rem;
}

.search-input input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.filter-group select {
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    background: var(--bg-primary);
    transition: var(--transition);
}

.filter-group select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Table Styles */
.table-container {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-tertiary);
}

.table-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.table-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-size-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.page-size-selector select {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
}

.table-wrapper {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.data-table th {
    background: var(--bg-tertiary);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    white-space: nowrap;
}

[dir="rtl"] .data-table th {
    text-align: right;
}

.data-table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: var(--transition);
}

.data-table th.sortable:hover {
    background: var(--border-color);
}

.data-table th.sortable {
    position: relative;
}

.data-table th.sortable i {
    margin-left: 0.5rem;
    color: var(--text-secondary);
    transition: var(--transition);
}

[dir="rtl"] .data-table th.sortable i {
    margin-left: 0;
    margin-right: 0.5rem;
}

.data-table th.sortable:hover i {
    color: var(--primary-blue);
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-light);
    vertical-align: top;
}

.data-table tbody tr {
    transition: var(--transition);
}

.data-table tbody tr:hover {
    background: var(--very-light-blue);
}

.customer-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.customer-name {
    font-weight: 500;
    color: var(--text-primary);
}

.customer-notes {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-style: italic;
}

.phone-link,
.email-link {
    color: var(--primary-blue);
    text-decoration: none;
    transition: var(--transition);
}

.phone-link:hover,
.email-link:hover {
    text-decoration: underline;
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-inactive {
    background: rgba(107, 114, 128, 0.1);
    color: var(--text-secondary);
}

.status-prospect {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.actions-cell {
    width: 120px;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.btn-action {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.btn-view {
    background: rgba(6, 182, 212, 0.1);
    color: var(--info-color);
}

.btn-view:hover {
    background: var(--info-color);
    color: var(--text-white);
}

.btn-edit {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.btn-edit:hover {
    background: var(--warning-color);
    color: var(--text-white);
}

.btn-delete {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.btn-delete:hover {
    background: var(--error-color);
    color: var(--text-white);
}

.table-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    background: var(--bg-tertiary);
}

/* Pagination Styles */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-primary);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.pagination-btn:hover:not(.disabled) {
    background: var(--primary-blue);
    color: var(--text-white);
    border-color: var(--primary-blue);
}

.pagination-btn.active {
    background: var(--primary-blue);
    color: var(--text-white);
    border-color: var(--primary-blue);
}

.pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-ellipsis {
    padding: 0.5rem;
    color: var(--text-secondary);
}

/* Search Results Styles */
.search-results {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1001;
}

.search-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-tertiary);
}

.search-query {
    font-weight: 600;
    color: var(--text-primary);
}

.search-count {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.search-section {
    border-bottom: 1px solid var(--border-light);
}

.search-section:last-child {
    border-bottom: none;
}

.search-section-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background: var(--bg-tertiary);
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.search-section-count {
    margin-left: auto;
    background: var(--primary-blue);
    color: var(--text-white);
    padding: 0.125rem 0.5rem;
    border-radius: 10px;
    font-size: 0.75rem;
}

[dir="rtl"] .search-section-count {
    margin-left: 0;
    margin-right: auto;
}

.search-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid var(--border-light);
}

.search-item:last-child {
    border-bottom: none;
}

.search-item:hover,
.search-item.active {
    background: var(--light-blue);
}

.search-item-content {
    flex: 1;
}

.search-item-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.search-item-title mark {
    background: var(--warning-color);
    color: var(--text-white);
    padding: 0.125rem 0.25rem;
    border-radius: 2px;
}

.search-item-subtitle {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.search-item-actions {
    display: flex;
    gap: 0.25rem;
}

.search-item-action {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border: none;
    border-radius: var(--border-radius);
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.search-item-action:hover {
    background: var(--primary-blue);
    color: var(--text-white);
}

.search-loading,
.search-error,
.search-no-results {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    color: var(--text-secondary);
}

.search-loading i {
    margin-right: 0.5rem;
    color: var(--primary-blue);
}

[dir="rtl"] .search-loading i {
    margin-right: 0;
    margin-left: 0.5rem;
}

.search-error i {
    margin-right: 0.5rem;
    color: var(--error-color);
}

[dir="rtl"] .search-error i {
    margin-right: 0;
    margin-left: 0.5rem;
}

.search-no-results {
    flex-direction: column;
    gap: 0.5rem;
}

.no-results-text {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.no-results-query {
    font-weight: 600;
    color: var(--text-primary);
}

.search-footer {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.search-view-all {
    background: var(--primary-blue);
    color: var(--text-white);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.search-view-all:hover {
    background: var(--primary-blue-dark);
}

/* Tab Navigation Styles */
.tab-navigation {
    display: flex;
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: 0.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    background: none;
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    flex: 1;
    justify-content: center;
}

.tab-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.tab-btn.active {
    background: var(--primary-blue);
    color: var(--text-white);
}

.tab-content {
    animation: fadeIn 0.3s ease-in-out;
}

/* Badge Styles */
.service-type-badge,
.contact-method-badge,
.task-count-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    background: var(--light-blue);
    color: var(--primary-blue);
}

.priority-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.priority-high {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.priority-medium {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.priority-low {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-pending {
    background: rgba(107, 114, 128, 0.1);
    color: var(--text-secondary);
}

.status-in_progress {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.status-completed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

/* Performance Styles */
.performance-content {
    padding: 0;
}

.performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.performance-widget {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.performance-widget h3 {
    background: var(--bg-tertiary);
    padding: 1rem 1.5rem;
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
}

.chart-container {
    padding: 1.5rem;
    min-height: 200px;
}

.simple-chart {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.chart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.chart-label {
    min-width: 100px;
    font-weight: 500;
    color: var(--text-primary);
}

.chart-bar {
    flex: 1;
    height: 20px;
    background: var(--bg-tertiary);
    border-radius: 10px;
    overflow: hidden;
}

.chart-fill {
    height: 100%;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    transition: var(--transition);
}

.chart-value {
    min-width: 60px;
    text-align: right;
    font-weight: 500;
    color: var(--text-secondary);
}

.completion-rate {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.rate-circle {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: var(--text-white);
}

.rate-percentage {
    font-size: 2rem;
    font-weight: 700;
}

.rate-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

.rate-breakdown {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
}

.rate-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.rate-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.rate-color.completed {
    background: var(--success-color);
}

.rate-color.in-progress {
    background: var(--warning-color);
}

.rate-color.pending {
    background: var(--text-secondary);
}

.rankings-container {
    padding: 1.5rem;
}

.rankings-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.ranking-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.ranking-item:hover {
    background: var(--light-blue);
}

.ranking-position {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--primary-blue);
    color: var(--text-white);
    border-radius: 50%;
    font-weight: 600;
    font-size: 0.875rem;
}

.ranking-info {
    flex: 1;
}

.ranking-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.ranking-stats {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.ranking-score {
    font-weight: 600;
    color: var(--primary-blue);
    font-size: 1.125rem;
}

/* Additional Action Button Styles */
.btn-assign {
    background: rgba(6, 182, 212, 0.1);
    color: var(--info-color);
}

.btn-assign:hover {
    background: var(--info-color);
    color: var(--text-white);
}

/* Employee and Task Info Styles */
.employee-info,
.supplier-info,
.task-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.employee-name,
.supplier-name,
.task-title {
    font-weight: 500;
    color: var(--text-primary);
}

.employee-email,
.supplier-notes,
.task-description {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* Widget Item Styles for Dashboard */
.widget-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-light);
}

.widget-item:last-child {
    border-bottom: none;
}

.item-info {
    flex: 1;
}

.item-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.item-subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.item-date {
    font-size: 0.875rem;
    color: var(--text-secondary);
    white-space: nowrap;
}

.no-data {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 2rem;
}

/* Performance Metrics */
.performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.metric {
    text-align: center;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
}

.metric-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-blue);
}
