// Universal Search System for CRM
class SearchManager {
    constructor() {
        this.searchInput = null;
        this.searchResults = null;
        this.searchTimeout = null;
        this.searchDelay = 300; // milliseconds
        this.maxResults = 10;
        this.isSearching = false;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.setupSearchElements();
    }
    
    setupSearchElements() {
        this.searchInput = document.getElementById('globalSearch');
        this.searchResults = document.getElementById('searchResults');
        
        if (this.searchInput) {
            this.searchInput.addEventListener('input', (e) => {
                this.handleSearchInput(e.target.value);
            });
            
            this.searchInput.addEventListener('focus', () => {
                if (this.searchInput.value.trim()) {
                    this.showSearchResults();
                }
            });
            
            this.searchInput.addEventListener('blur', (e) => {
                // Delay hiding to allow clicking on results
                setTimeout(() => {
                    if (!this.searchResults.contains(document.activeElement)) {
                        this.hideSearchResults();
                    }
                }, 200);
            });
            
            this.searchInput.addEventListener('keydown', (e) => {
                this.handleKeyNavigation(e);
            });
        }
    }
    
    bindEvents() {
        // Close search results when clicking outside
        document.addEventListener('click', (e) => {
            if (this.searchResults && !this.searchResults.contains(e.target) && 
                this.searchInput && !this.searchInput.contains(e.target)) {
                this.hideSearchResults();
            }
        });
        
        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.searchResults && this.searchResults.style.display !== 'none') {
                this.hideSearchResults();
                if (this.searchInput) {
                    this.searchInput.blur();
                }
            }
        });
    }
    
    handleSearchInput(query) {
        // Clear previous timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        
        // Set new timeout for delayed search
        this.searchTimeout = setTimeout(() => {
            this.performSearch(query);
        }, this.searchDelay);
    }
    
    async performSearch(query) {
        if (!query || query.trim().length < 2) {
            this.hideSearchResults();
            return;
        }
        
        this.isSearching = true;
        this.showLoadingState();
        
        try {
            const storage = getStorageManager();
            const results = storage.searchAll(query);
            
            this.displaySearchResults(results, query);
            this.isSearching = false;
        } catch (error) {
            console.error('Search error:', error);
            this.showErrorState();
            this.isSearching = false;
        }
    }
    
    displaySearchResults(results, query) {
        if (!this.searchResults) return;
        
        const totalResults = results.customers.length + results.suppliers.length + 
                           results.telesales.length + results.tasks.length;
        
        if (totalResults === 0) {
            this.showNoResults(query);
            return;
        }
        
        let html = '';
        
        // Add search header
        html += `
            <div class="search-header">
                <span class="search-query">${this.escapeHtml(query)}</span>
                <span class="search-count">${totalResults} ${getTranslation('results')}</span>
            </div>
        `;
        
        // Add customers results
        if (results.customers.length > 0) {
            html += this.renderSearchSection('customers', results.customers.slice(0, this.maxResults), query);
        }
        
        // Add suppliers results
        if (results.suppliers.length > 0) {
            html += this.renderSearchSection('suppliers', results.suppliers.slice(0, this.maxResults), query);
        }
        
        // Add telesales results
        if (results.telesales.length > 0) {
            html += this.renderSearchSection('telesales', results.telesales.slice(0, this.maxResults), query);
        }
        
        // Add tasks results
        if (results.tasks.length > 0) {
            html += this.renderSearchSection('tasks', results.tasks.slice(0, this.maxResults), query);
        }
        
        // Add view all link if there are more results
        if (totalResults > this.maxResults) {
            html += `
                <div class="search-footer">
                    <button class="search-view-all" onclick="searchManager.viewAllResults('${this.escapeHtml(query)}')">
                        ${getTranslation('view_all_results')} (${totalResults})
                    </button>
                </div>
            `;
        }
        
        this.searchResults.innerHTML = html;
        this.showSearchResults();
        this.bindResultEvents();
    }
    
    renderSearchSection(type, items, query) {
        const sectionTitle = getTranslation(type);
        let html = `
            <div class="search-section">
                <div class="search-section-header">
                    <i class="${this.getSectionIcon(type)}"></i>
                    <span>${sectionTitle}</span>
                    <span class="search-section-count">${items.length}</span>
                </div>
                <div class="search-section-items">
        `;
        
        items.forEach(item => {
            html += this.renderSearchItem(type, item, query);
        });
        
        html += `
                </div>
            </div>
        `;
        
        return html;
    }
    
    renderSearchItem(type, item, query) {
        const highlightedText = this.highlightSearchTerm(this.getItemDisplayText(type, item), query);
        const subtitle = this.getItemSubtitle(type, item);
        
        return `
            <div class="search-item" data-type="${type}" data-id="${item.id}">
                <div class="search-item-content">
                    <div class="search-item-title">${highlightedText}</div>
                    ${subtitle ? `<div class="search-item-subtitle">${subtitle}</div>` : ''}
                </div>
                <div class="search-item-actions">
                    <button class="search-item-action" onclick="searchManager.viewItem('${type}', '${item.id}')" title="${getTranslation('view')}">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
        `;
    }
    
    getItemDisplayText(type, item) {
        switch (type) {
            case 'customers':
                return item.fullName || item.name || '';
            case 'suppliers':
                return item.supplierName || item.name || '';
            case 'telesales':
                return item.employeeName || item.name || '';
            case 'tasks':
                return item.taskTitle || item.title || '';
            default:
                return '';
        }
    }
    
    getItemSubtitle(type, item) {
        switch (type) {
            case 'customers':
                return `${item.mobilePhone || ''} • ${item.city || ''} • ${item.businessType || ''}`;
            case 'suppliers':
                return `${item.serviceType || ''} • ${item.city || ''} • ${item.contactMethod || ''}`;
            case 'telesales':
                return `${item.position || ''} • ${item.department || ''}`;
            case 'tasks':
                return `${item.priority || ''} • ${item.status || ''} • ${this.formatDate(item.deadline)}`;
            default:
                return '';
        }
    }
    
    getSectionIcon(type) {
        const icons = {
            customers: 'fas fa-users',
            suppliers: 'fas fa-truck',
            telesales: 'fas fa-headset',
            tasks: 'fas fa-tasks'
        };
        return icons[type] || 'fas fa-circle';
    }
    
    highlightSearchTerm(text, query) {
        if (!text || !query) return text;
        
        const regex = new RegExp(`(${this.escapeRegex(query)})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }
    
    showLoadingState() {
        if (!this.searchResults) return;
        
        this.searchResults.innerHTML = `
            <div class="search-loading">
                <i class="fas fa-spinner fa-spin"></i>
                <span>${getTranslation('searching')}</span>
            </div>
        `;
        this.showSearchResults();
    }
    
    showErrorState() {
        if (!this.searchResults) return;
        
        this.searchResults.innerHTML = `
            <div class="search-error">
                <i class="fas fa-exclamation-triangle"></i>
                <span>${getTranslation('search_error')}</span>
            </div>
        `;
        this.showSearchResults();
    }
    
    showNoResults(query) {
        if (!this.searchResults) return;
        
        this.searchResults.innerHTML = `
            <div class="search-no-results">
                <i class="fas fa-search"></i>
                <div class="no-results-text">
                    <div>${getTranslation('no_results_found')}</div>
                    <div class="no-results-query">"${this.escapeHtml(query)}"</div>
                </div>
            </div>
        `;
        this.showSearchResults();
    }
    
    showSearchResults() {
        if (this.searchResults) {
            this.searchResults.style.display = 'block';
        }
    }
    
    hideSearchResults() {
        if (this.searchResults) {
            this.searchResults.style.display = 'none';
        }
    }
    
    bindResultEvents() {
        // Bind click events to search items
        this.searchResults.querySelectorAll('.search-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (!e.target.closest('.search-item-actions')) {
                    const type = item.getAttribute('data-type');
                    const id = item.getAttribute('data-id');
                    this.viewItem(type, id);
                }
            });
        });
    }
    
    viewItem(type, id) {
        // Hide search results
        this.hideSearchResults();
        
        // Navigate to the appropriate module and show the item
        if (window.app && window.app.navigateToModule) {
            window.app.navigateToModule(type, { action: 'view', id: id });
        } else {
            // Fallback navigation
            this.navigateToItem(type, id);
        }
    }
    
    navigateToItem(type, id) {
        // Switch to the appropriate module
        const moduleMap = {
            customers: 'customers',
            suppliers: 'suppliers',
            telesales: 'telesales',
            tasks: 'telesales' // Tasks are part of telesales module
        };
        
        const targetModule = moduleMap[type];
        if (targetModule) {
            // Trigger navigation event
            document.dispatchEvent(new CustomEvent('navigateToModule', {
                detail: { module: targetModule, action: 'view', id: id, type: type }
            }));
        }
    }
    
    viewAllResults(query) {
        // Hide search results
        this.hideSearchResults();
        
        // Navigate to a dedicated search results page or show in current module
        if (window.app && window.app.showSearchResults) {
            window.app.showSearchResults(query);
        }
    }
    
    handleKeyNavigation(e) {
        if (!this.searchResults || this.searchResults.style.display === 'none') {
            return;
        }
        
        const items = this.searchResults.querySelectorAll('.search-item');
        const currentActive = this.searchResults.querySelector('.search-item.active');
        let activeIndex = currentActive ? Array.from(items).indexOf(currentActive) : -1;
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                activeIndex = Math.min(activeIndex + 1, items.length - 1);
                this.setActiveItem(items, activeIndex);
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                activeIndex = Math.max(activeIndex - 1, 0);
                this.setActiveItem(items, activeIndex);
                break;
                
            case 'Enter':
                e.preventDefault();
                if (currentActive) {
                    const type = currentActive.getAttribute('data-type');
                    const id = currentActive.getAttribute('data-id');
                    this.viewItem(type, id);
                }
                break;
        }
    }
    
    setActiveItem(items, index) {
        // Remove active class from all items
        items.forEach(item => item.classList.remove('active'));
        
        // Add active class to selected item
        if (items[index]) {
            items[index].classList.add('active');
            items[index].scrollIntoView({ block: 'nearest' });
        }
    }
    
    // Utility methods
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    
    formatDate(dateString) {
        if (!dateString) return '';
        
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString(getCurrentLanguage() === 'ar' ? 'ar-SA' : 'en-US');
        } catch (error) {
            return dateString;
        }
    }
    
    // Advanced search methods
    performAdvancedSearch(filters) {
        const storage = getStorageManager();
        let results = {
            customers: storage.getCustomers(),
            suppliers: storage.getSuppliers(),
            telesales: storage.getTelesales(),
            tasks: storage.getTasks()
        };
        
        // Apply filters
        Object.keys(filters).forEach(filterType => {
            const filterValue = filters[filterType];
            if (filterValue && filterValue.trim()) {
                results = this.applyFilter(results, filterType, filterValue);
            }
        });
        
        return results;
    }
    
    applyFilter(results, filterType, filterValue) {
        // Implementation depends on filter type
        // This is a basic example
        const lowerFilterValue = filterValue.toLowerCase();
        
        Object.keys(results).forEach(type => {
            results[type] = results[type].filter(item => {
                // Apply type-specific filtering logic
                return this.itemMatchesFilter(item, filterType, lowerFilterValue);
            });
        });
        
        return results;
    }
    
    itemMatchesFilter(item, filterType, filterValue) {
        // Basic implementation - can be extended
        switch (filterType) {
            case 'status':
                return item.status && item.status.toLowerCase().includes(filterValue);
            case 'city':
                return item.city && item.city.toLowerCase().includes(filterValue);
            case 'type':
                return (item.businessType && item.businessType.toLowerCase().includes(filterValue)) ||
                       (item.serviceType && item.serviceType.toLowerCase().includes(filterValue));
            default:
                return true;
        }
    }
    
    // Clear search
    clearSearch() {
        if (this.searchInput) {
            this.searchInput.value = '';
        }
        this.hideSearchResults();
    }
}

// Global search manager instance
let searchManager;

// Initialize search system
function initializeSearch() {
    searchManager = new SearchManager();
    return searchManager;
}

// Helper functions for global access
function performSearch(query) {
    return searchManager ? searchManager.performSearch(query) : null;
}

function clearSearch() {
    if (searchManager) {
        searchManager.clearSearch();
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SearchManager, initializeSearch, performSearch, clearSearch };
}
