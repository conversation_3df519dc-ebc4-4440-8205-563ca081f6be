/* Responsive Design for CRM System */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .dashboard-stats {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .dashboard-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .main-content {
        padding: 2.5rem;
    }
}

/* Desktop (992px to 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {
    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .dashboard-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .search-container {
        max-width: 400px;
    }
}

/* Tablet (768px to 991px) */
@media (max-width: 991px) and (min-width: 768px) {
    .sidebar {
        width: 70px;
    }
    
    .sidebar .nav-link span,
    .sidebar .nav-badge {
        display: none;
    }
    
    .main-content {
        margin-left: 70px;
        padding: 1.5rem;
    }
    
    [dir="rtl"] .main-content {
        margin-left: 0;
        margin-right: 70px;
    }
    
    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .stat-content h3 {
        font-size: 1.5rem;
    }
    
    .header-content {
        padding: 0 1rem;
    }
    
    .search-container {
        max-width: 300px;
        margin: 0 1rem;
    }
    
    .module-header h2 {
        font-size: 1.75rem;
    }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 767px) and (min-width: 576px) {
    .sidebar {
        transform: translateX(-100%);
        width: 280px;
        z-index: 1500;
    }
    
    [dir="rtl"] .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: 1rem;
    }
    
    [dir="rtl"] .main-content {
        margin-right: 0;
    }
    
    .dashboard-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .stat-card {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
    
    .header-content {
        padding: 0 0.75rem;
    }
    
    .header-center {
        display: none;
    }
    
    .module-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .module-header h2 {
        font-size: 1.5rem;
    }
    
    .module-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    /* Mobile Search */
    .mobile-search {
        display: block;
        padding: 1rem;
        background: var(--bg-primary);
        border-bottom: 1px solid var(--border-color);
        margin: -1rem -1rem 1rem -1rem;
    }
    
    .mobile-search .search-box {
        position: relative;
    }
    
    .mobile-search .search-box input {
        width: 100%;
        padding: 0.75rem 1rem 0.75rem 3rem;
        border: 2px solid var(--border-color);
        border-radius: var(--border-radius);
        font-size: 1rem;
    }
    
    [dir="rtl"] .mobile-search .search-box input {
        padding: 0.75rem 3rem 0.75rem 1rem;
    }
    
    /* Login Page Mobile */
    .login-container {
        max-width: 100%;
        margin: 0 1rem;
    }
    
    .login-header {
        padding: 1.5rem;
    }
    
    .login-header h1 {
        font-size: 1.25rem;
    }
    
    .login-form {
        padding: 1.5rem;
    }
    
    .language-toggle {
        top: 0.75rem;
        right: 0.75rem;
    }
    
    [dir="rtl"] .language-toggle {
        right: auto;
        left: 0.75rem;
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
    .sidebar {
        width: 100%;
        transform: translateX(-100%);
    }
    
    [dir="rtl"] .sidebar {
        transform: translateX(100%);
    }
    
    .main-content {
        padding: 0.75rem;
    }
    
    .dashboard-stats {
        gap: 0.75rem;
    }
    
    .dashboard-grid {
        gap: 0.75rem;
    }
    
    .stat-card {
        padding: 0.75rem;
        gap: 0.5rem;
    }
    
    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .stat-content h3 {
        font-size: 1.25rem;
    }
    
    .stat-content p {
        font-size: 0.875rem;
    }
    
    .header-content {
        padding: 0 0.5rem;
    }
    
    .header-actions {
        gap: 0.25rem;
    }
    
    .btn-icon {
        padding: 0.5rem;
        font-size: 1rem;
    }
    
    .user-profile {
        padding: 0.25rem 0.5rem;
    }
    
    .user-profile span {
        display: none;
    }
    
    .module-header h2 {
        font-size: 1.25rem;
    }
    
    .dashboard-widget h3 {
        font-size: 1rem;
        padding: 0.75rem 1rem;
    }
    
    .widget-content {
        padding: 1rem;
        min-height: 150px;
    }
    
    /* Login Page Small Mobile */
    .login-container {
        margin: 0 0.5rem;
    }
    
    .login-header {
        padding: 1rem;
    }
    
    .login-header .logo i {
        font-size: 2rem;
    }
    
    .login-header h1 {
        font-size: 1.125rem;
    }
    
    .login-form {
        padding: 1rem;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .input-group input,
    .input-group select {
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        font-size: 0.875rem;
    }
    
    [dir="rtl"] .input-group input,
    [dir="rtl"] .input-group select {
        padding: 0.75rem 2.5rem 0.75rem 1rem;
    }
    
    .input-group i {
        left: 0.75rem;
        font-size: 0.875rem;
    }
    
    [dir="rtl"] .input-group i {
        left: auto;
        right: 0.75rem;
    }
    
    .btn-login {
        padding: 0.875rem;
        font-size: 1rem;
    }
    
    .login-help {
        padding: 0.75rem;
    }
    
    .login-help small {
        font-size: 0.75rem;
    }
}

/* Mobile Sidebar Overlay */
@media (max-width: 767px) {
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1400;
        display: none;
    }
    
    .sidebar-overlay.active {
        display: block;
    }
    
    .sidebar.mobile-open {
        box-shadow: var(--shadow-xl);
    }
}

/* Print Styles */
@media print {
    .main-header,
    .sidebar,
    .header-actions,
    .module-actions,
    .btn-icon,
    .sidebar-toggle {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .dashboard-stats,
    .dashboard-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }
    
    .stat-card,
    .dashboard-widget {
        box-shadow: none !important;
        border: 1px solid var(--border-color) !important;
        break-inside: avoid;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
        --bg-tertiary: #f0f0f0;
    }
    
    .stat-card,
    .dashboard-widget {
        border: 2px solid var(--border-color);
    }
    
    .nav-link:hover,
    .nav-link.active {
        background: #000000;
        color: #ffffff;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark Mode Support (for future implementation) */
@media (prefers-color-scheme: dark) {
    .auto-theme {
        --bg-primary: #1f2937;
        --bg-secondary: #111827;
        --bg-tertiary: #374151;
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --border-color: #374151;
    }
}

/* Focus Styles for Accessibility */
button:focus,
input:focus,
select:focus,
a:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* Skip to Content Link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-blue);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
}

.skip-link:focus {
    top: 6px;
}

/* Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
