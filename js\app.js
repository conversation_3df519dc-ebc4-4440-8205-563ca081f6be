// دوال مساعدة للتوافق مع النظام الجديد
function getStorageManager() {
    return window.storageManager || {
        getStatistics: () => ({
            customers: { total: 0, active: 0 },
            suppliers: { total: 0 },
            telesales: { total: 0 },
            tasks: { total: 0, completed: 0 }
        }),
        getCustomers: () => [],
        getTasks: () => [],
        createBackup: () => ({})
    };
}

function getTranslation(key) {
    const translations = {
        'last_update': 'آخر تحديث',
        'access_denied': 'ليس لديك صلاحية للوصول',
        'app_title': 'نظام إدارة علاقات العملاء',
        'dashboard': 'لوحة التحكم',
        'customers': 'العملاء',
        'suppliers': 'الموردين',
        'telesales': 'المبيعات الهاتفية',
        'reports': 'التقارير',
        'settings': 'الإعدادات',
        'no_recent_customers': 'لا توجد عملاء جدد',
        'no_pending_tasks': 'لا توجد مهام معلقة',
        'active_customers': 'العملاء النشطون',
        'completed_tasks': 'المهام المكتملة',
        'total_suppliers': 'إجمالي الموردين',
        'backup_created': 'تم إنشاء النسخة الاحتياطية',
        'backup_failed': 'فشل في إنشاء النسخة الاحتياطية',
        'confirm_logout': 'هل أنت متأكد من تسجيل الخروج؟'
    };
    return translations[key] || key;
}

function canAccessModule(moduleName) {
    // تحقق بسيط من الصلاحيات - يمكن تطويره لاحقاً
    return true;
}

// Main Application Controller for CRM System
class CRMApplication {
    constructor() {
        this.currentModule = 'dashboard';
        this.modules = {};
        this.isInitialized = false;

        this.init();
    }

    // التحقق من المصادقة - متوافق مع النظام الجديد
    checkAuth() {
        try {
            const isLoggedIn = localStorage.getItem('crmLoggedIn');
            const sessionData = localStorage.getItem('crmSession');

            if (isLoggedIn !== 'true' || !sessionData) {
                return false;
            }

            const session = JSON.parse(sessionData);
            if (!session.user || !session.user.username || !session.expiresAt) {
                return false;
            }

            // التحقق من انتهاء صلاحية الجلسة
            const expiryDate = new Date(session.expiresAt);
            const currentDate = new Date();

            if (expiryDate <= currentDate) {
                // مسح الجلسة المنتهية الصلاحية
                localStorage.removeItem('crmLoggedIn');
                localStorage.removeItem('crmSession');
                return false;
            }

            return true;
        } catch (error) {
            console.error('خطأ في التحقق من المصادقة:', error);
            return false;
        }
    }

    // الحصول على المستخدم الحالي - متوافق مع النظام الجديد
    getCurrentUser() {
        try {
            const sessionData = localStorage.getItem('crmSession');
            if (sessionData) {
                const session = JSON.parse(sessionData);
                return session.user;
            }
            return null;
        } catch (error) {
            console.error('خطأ في الحصول على بيانات المستخدم:', error);
            return null;
        }
    }

    async init() {
        try {
            // Check authentication - updated for new system
            if (!this.checkAuth()) {
                window.location.href = 'login.html';
                return;
            }

            // Initialize all systems
            await this.initializeSystems();
            
            // Setup UI
            this.setupUI();
            
            // Load initial module
            this.loadModule('dashboard');
            
            // Mark as initialized
            this.isInitialized = true;
            
            console.log('CRM Application initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize CRM Application:', error);
            this.showError('Failed to initialize application');
        }
    }
    
    async initializeSystems() {
        // Initialize storage system
        if (!window.storageManager) {
            window.storageManager = initializeStorage();
        }
        
        // Initialize search system
        if (!window.searchManager) {
            window.searchManager = initializeSearch();
        }
        
        // Initialize export system
        if (!window.exportManager) {
            window.exportManager = initializeExport();
        }
        
        // Initialize module managers
        if (!window.customerManager) {
            window.customerManager = initializeCustomers();
        }

        if (!window.supplierManager) {
            window.supplierManager = initializeSuppliers();
        }

        if (!window.telesalesManager) {
            window.telesalesManager = initializeTelesales();
        }
        
        // Register modules
        this.modules = {
            dashboard: this.loadDashboardModule.bind(this),
            customers: this.loadCustomersModule.bind(this),
            suppliers: this.loadSuppliersModule.bind(this),
            telesales: this.loadTelesalesModule.bind(this),
            reports: this.loadReportsModule.bind(this),
            settings: this.loadSettingsModule.bind(this)
        };
    }
    
    setupUI() {
        // Setup current user info
        this.updateUserInfo();
        
        // Setup navigation
        this.setupNavigation();
        
        // Setup header actions
        this.setupHeaderActions();
        
        // Setup sidebar toggle
        this.setupSidebarToggle();
        
        // Setup role-based UI
        this.setupRoleBasedUI();
        
        // Update statistics
        this.updateGlobalStatistics();
        
        // Setup auto-refresh
        this.setupAutoRefresh();
    }
    
    updateUserInfo() {
        const currentUser = this.getCurrentUser();
        if (currentUser) {
            const userNameElement = document.getElementById('currentUserName');
            if (userNameElement) {
                userNameElement.textContent = currentUser.name || currentUser.username;
            }
        }
    }
    
    setupNavigation() {
        // Navigation link clicks
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const module = link.getAttribute('data-module');
                if (module && this.modules[module]) {
                    this.navigateToModule(module);
                }
            });
        });
        
        // Listen for navigation events
        document.addEventListener('navigateToModule', (e) => {
            this.navigateToModule(e.detail.module, e.detail);
        });
    }
    
    setupHeaderActions() {
        // Language toggle
        const languageToggle = document.getElementById('languageToggle');
        if (languageToggle) {
            languageToggle.addEventListener('click', () => {
                if (window.languageManager) {
                    languageManager.toggleLanguage();
                }
            });
        }
        
        // Export data
        const exportData = document.getElementById('exportData');
        if (exportData) {
            exportData.addEventListener('click', () => {
                if (window.exportManager) {
                    exportManager.showExportModal();
                }
            });
        }
        
        // Backup data
        const backupData = document.getElementById('backupData');
        if (backupData) {
            backupData.addEventListener('click', () => {
                this.createBackup();
            });
        }
        
        // User profile dropdown
        const userProfile = document.getElementById('userProfile');
        const userDropdown = document.getElementById('userDropdown');
        
        if (userProfile && userDropdown) {
            userProfile.addEventListener('click', (e) => {
                e.stopPropagation();
                userDropdown.style.display = userDropdown.style.display === 'block' ? 'none' : 'block';
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', () => {
                userDropdown.style.display = 'none';
            });
        }
        
        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });
        }
    }
    
    setupSidebarToggle() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
                
                // Save sidebar state
                localStorage.setItem('sidebar_collapsed', sidebar.classList.contains('collapsed'));
            });
            
            // Restore sidebar state
            const isCollapsed = localStorage.getItem('sidebar_collapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
            }
        }
        
        // Mobile sidebar toggle
        if (window.innerWidth <= 767) {
            this.setupMobileSidebar();
        }
        
        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth <= 767) {
                this.setupMobileSidebar();
            } else {
                this.removeMobileSidebar();
            }
        });
    }
    
    setupMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const sidebarToggle = document.getElementById('sidebarToggle');
        
        if (sidebar && sidebarToggle) {
            // Create overlay if it doesn't exist
            let overlay = document.querySelector('.sidebar-overlay');
            if (!overlay) {
                overlay = document.createElement('div');
                overlay.className = 'sidebar-overlay';
                document.body.appendChild(overlay);
            }
            
            // Toggle mobile sidebar
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('mobile-open');
                overlay.classList.toggle('active');
            });
            
            // Close sidebar when clicking overlay
            overlay.addEventListener('click', () => {
                sidebar.classList.remove('mobile-open');
                overlay.classList.remove('active');
            });
            
            // Close sidebar when clicking nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    sidebar.classList.remove('mobile-open');
                    overlay.classList.remove('active');
                });
            });
        }
    }
    
    removeMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        
        if (sidebar) {
            sidebar.classList.remove('mobile-open');
        }
        
        if (overlay) {
            overlay.classList.remove('active');
        }
    }
    
    setupRoleBasedUI() {
        const currentUser = this.getCurrentUser();
        if (!currentUser) return;
        
        // Hide elements based on role permissions
        if (window.authManager) {
            authManager.hideElementsForRole();
        }
        
        // Update navigation badges
        this.updateNavigationBadges();
    }
    
    updateNavigationBadges() {
        const storage = getStorageManager();
        const stats = storage.getStatistics();
        
        // Update customer count
        const customerBadge = document.getElementById('customerCount');
        if (customerBadge) {
            customerBadge.textContent = stats.customers.total;
        }
        
        // Update supplier count
        const supplierBadge = document.getElementById('supplierCount');
        if (supplierBadge) {
            supplierBadge.textContent = stats.suppliers.total;
        }
        
        // Update telesales count
        const telesalesBadge = document.getElementById('telesalesCount');
        if (telesalesBadge) {
            telesalesBadge.textContent = stats.telesales.total;
        }
    }
    
    updateGlobalStatistics() {
        const storage = getStorageManager();
        const stats = storage.getStatistics();
        
        // Update dashboard stats
        const totalCustomers = document.getElementById('totalCustomers');
        const totalSuppliers = document.getElementById('totalSuppliers');
        const totalTelesales = document.getElementById('totalTelesales');
        const totalTasks = document.getElementById('totalTasks');
        
        if (totalCustomers) totalCustomers.textContent = stats.customers.total;
        if (totalSuppliers) totalSuppliers.textContent = stats.suppliers.total;
        if (totalTelesales) totalTelesales.textContent = stats.telesales.total;
        if (totalTasks) totalTasks.textContent = stats.tasks.total;
        
        // Update last update time
        const lastUpdate = document.getElementById('lastUpdate');
        if (lastUpdate) {
            lastUpdate.textContent = `${getTranslation('last_update')}: ${new Date().toLocaleTimeString()}`;
        }
    }
    
    setupAutoRefresh() {
        // Auto-refresh statistics every 5 minutes
        setInterval(() => {
            this.updateGlobalStatistics();
            this.updateNavigationBadges();
        }, 5 * 60 * 1000);
    }
    
    // Module loading methods
    navigateToModule(moduleName, options = {}) {
        if (!this.modules[moduleName]) {
            console.error(`Module ${moduleName} not found`);
            return;
        }
        
        // Check permissions
        if (!canAccessModule(moduleName)) {
            this.showError(getTranslation('access_denied'));
            return;
        }
        
        // Update active navigation
        this.updateActiveNavigation(moduleName);
        
        // Hide all modules
        document.querySelectorAll('.module').forEach(module => {
            module.classList.remove('active');
        });
        
        // Show target module
        const targetModule = document.getElementById(`${moduleName}Module`);
        if (targetModule) {
            targetModule.classList.add('active');
        }
        
        // Load module content
        this.modules[moduleName](options);
        
        // Update current module
        this.currentModule = moduleName;
        
        // Update page title
        document.title = `${getTranslation(moduleName)} - ${getTranslation('app_title')}`;
    }
    
    updateActiveNavigation(moduleName) {
        // Remove active class from all nav links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        // Add active class to current module nav link
        const activeLink = document.querySelector(`.nav-link[data-module="${moduleName}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }
    
    // Module loaders
    loadDashboardModule(options = {}) {
        this.loadDashboardContent();
    }
    
    loadCustomersModule(options = {}) {
        if (window.customerManager) {
            customerManager.showCustomersModule();
        }
    }
    
    loadSuppliersModule(options = {}) {
        if (window.supplierManager) {
            supplierManager.showSuppliersModule();
        }
    }

    loadTelesalesModule(options = {}) {
        if (window.telesalesManager) {
            telesalesManager.showTelesalesModule();
        }
    }
    
    loadReportsModule(options = {}) {
        // Implementation for reports module
        console.log('Loading reports module');
    }
    
    loadSettingsModule(options = {}) {
        // Implementation for settings module
        console.log('Loading settings module');
    }
    
    loadDashboardContent() {
        // Load recent customers
        this.loadRecentCustomers();
        
        // Load pending tasks
        this.loadPendingTasks();
        
        // Load performance overview
        this.loadPerformanceOverview();
        
        // Update statistics
        this.updateGlobalStatistics();
    }
    
    loadRecentCustomers() {
        const storage = getStorageManager();
        const customers = storage.getCustomers()
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
            .slice(0, 5);
        
        const container = document.getElementById('recentCustomers');
        if (container) {
            if (customers.length === 0) {
                container.innerHTML = `<p class="no-data">${getTranslation('no_recent_customers')}</p>`;
            } else {
                container.innerHTML = customers.map(customer => `
                    <div class="widget-item">
                        <div class="item-info">
                            <div class="item-title">${customer.fullName || ''}</div>
                            <div class="item-subtitle">${customer.city || ''} • ${customer.businessType || ''}</div>
                        </div>
                        <div class="item-date">${new Date(customer.createdAt).toLocaleDateString()}</div>
                    </div>
                `).join('');
            }
        }
    }
    
    loadPendingTasks() {
        const storage = getStorageManager();
        const tasks = storage.getTasks()
            .filter(task => task.status === 'pending')
            .sort((a, b) => new Date(a.deadline) - new Date(b.deadline))
            .slice(0, 5);
        
        const container = document.getElementById('pendingTasks');
        if (container) {
            if (tasks.length === 0) {
                container.innerHTML = `<p class="no-data">${getTranslation('no_pending_tasks')}</p>`;
            } else {
                container.innerHTML = tasks.map(task => `
                    <div class="widget-item">
                        <div class="item-info">
                            <div class="item-title">${task.taskTitle || task.title || ''}</div>
                            <div class="item-subtitle">${task.priority || ''} • ${task.assignedTo || ''}</div>
                        </div>
                        <div class="item-date">${task.deadline ? new Date(task.deadline).toLocaleDateString() : ''}</div>
                    </div>
                `).join('');
            }
        }
    }
    
    loadPerformanceOverview() {
        const storage = getStorageManager();
        const stats = storage.getStatistics();
        
        const container = document.getElementById('performanceOverview');
        if (container) {
            container.innerHTML = `
                <div class="performance-metrics">
                    <div class="metric">
                        <div class="metric-label">${getTranslation('active_customers')}</div>
                        <div class="metric-value">${stats.customers.active}</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">${getTranslation('completed_tasks')}</div>
                        <div class="metric-value">${stats.tasks.completed}</div>
                    </div>
                    <div class="metric">
                        <div class="metric-label">${getTranslation('total_suppliers')}</div>
                        <div class="metric-value">${stats.suppliers.total}</div>
                    </div>
                </div>
            `;
        }
    }
    
    // Utility methods
    createBackup() {
        try {
            const storage = getStorageManager();
            const backupData = storage.createBackup();
            
            // Download backup file
            const dataStr = JSON.stringify(backupData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `CRM-Backup-${new Date().toISOString().slice(0, 10)}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            URL.revokeObjectURL(url);
            
            this.showSuccess(getTranslation('backup_created'));
            
        } catch (error) {
            console.error('Backup error:', error);
            this.showError(getTranslation('backup_failed'));
        }
    }
    
    logout() {
        if (confirm(getTranslation('confirm_logout'))) {
            // مسح بيانات الجلسة
            localStorage.removeItem('crmLoggedIn');
            localStorage.removeItem('crmSession');
            localStorage.removeItem('crmLastActivity');

            // إعادة التوجيه إلى صفحة تسجيل الدخول
            window.location.href = 'login.html';
        }
    }
    
    showError(message) {
        if (window.exportManager) {
            exportManager.showNotification(message, 'error');
        } else {
            alert(message);
        }
    }
    
    showSuccess(message) {
        if (window.exportManager) {
            exportManager.showNotification(message, 'success');
        } else {
            alert(message);
        }
    }
    
    showWarning(message) {
        if (window.exportManager) {
            exportManager.showNotification(message, 'warning');
        } else {
            alert(message);
        }
    }
    
    // Public API methods
    getCurrentModule() {
        return this.currentModule;
    }
    
    refreshCurrentModule() {
        this.navigateToModule(this.currentModule);
    }
    
    showSearchResults(query) {
        // Implementation for showing search results
        console.log('Show search results for:', query);
    }
}

// Global application instance
let app;

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize language system first
    initializeLanguage();
    
    // Initialize authentication
    initializeAuth();
    
    // Initialize and start the application
    app = new CRMApplication();
    
    // Make app globally available
    window.app = app;
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CRMApplication };
}
