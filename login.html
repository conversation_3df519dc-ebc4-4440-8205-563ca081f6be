<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام CRM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            padding: 3rem;
            border-radius: 25px;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 500px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .logo {
            margin-bottom: 2.5rem;
        }

        .logo i {
            font-size: 5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .logo h1 {
            color: #333;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .logo p {
            color: #666;
            font-size: 1.1rem;
        }

        .form {
            text-align: right;
        }

        .form-group {
            margin-bottom: 1.8rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.8rem;
            color: #333;
            font-weight: 600;
            font-size: 1rem;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 1.2rem 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(248, 249, 250, 0.8);
            color: #333;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1.3rem;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .login-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.4);
        }

        .login-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .quick-login {
            margin-top: 2.5rem;
            padding: 2rem;
            background: rgba(248, 249, 250, 0.6);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.5);
        }

        .quick-login h3 {
            color: #495057;
            margin-bottom: 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .quick-btn {
            width: 100%;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 10px;
            margin: 0.6rem 0;
            cursor: pointer;
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .quick-btn:hover {
            background: linear-gradient(135deg, #218838, #1ea085);
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(40, 167, 69, 0.3);
        }

        .quick-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            margin: 1.5rem 0;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            text-align: center;
            font-weight: 500;
            font-size: 1rem;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message.loading {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .credentials {
            background: rgba(233, 236, 239, 0.8);
            padding: 1.5rem;
            border-radius: 15px;
            margin-top: 1.5rem;
            font-size: 0.9rem;
            text-align: right;
            border: 1px solid rgba(255, 255, 255, 0.5);
        }

        .credentials h4 {
            color: #495057;
            margin-bottom: 1rem;
            text-align: center;
            font-weight: 600;
        }

        .credential {
            margin: 0.5rem 0;
            color: #6c757d;
            padding: 0.3rem 0;
        }

        .credential strong {
            color: #495057;
        }

        @media (max-width: 600px) {
            .login-container {
                padding: 2rem;
                margin: 10px;
                border-radius: 20px;
            }

            .logo h1 {
                font-size: 1.6rem;
            }

            .logo i {
                font-size: 4rem;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <i class="fas fa-users-cog"></i>
            <h1>نظام إدارة علاقات العملاء</h1>
            <p>مرحباً بك في نظام CRM المتطور</p>
        </div>

        <form class="form" id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" required placeholder="أدخل اسم المستخدم">
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" required placeholder="أدخل كلمة المرور">
            </div>

            <div class="form-group">
                <label for="role">الدور الوظيفي:</label>
                <select id="role" required>
                    <option value="">-- اختر الدور الوظيفي --</option>
                    <option value="manager">مدير النظام</option>
                    <option value="supervisor">مشرف المبيعات</option>
                    <option value="telesales">موظف المبيعات الهاتفية</option>
                </select>
            </div>

            <button type="submit" class="login-btn" id="loginBtn">
                <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
            </button>
        </form>

        <div id="messageArea"></div>

        <div class="quick-login">
            <h3><i class="fas fa-bolt"></i> دخول سريع للتجربة</h3>
            <button type="button" class="quick-btn" onclick="quickLogin('admin', 'admin123', 'manager')">
                <i class="fas fa-crown"></i> دخول كمدير النظام
            </button>
            <button type="button" class="quick-btn" onclick="quickLogin('supervisor', 'super123', 'supervisor')">
                <i class="fas fa-user-tie"></i> دخول كمشرف المبيعات
            </button>
            <button type="button" class="quick-btn" onclick="quickLogin('sales', 'sales123', 'telesales')">
                <i class="fas fa-headset"></i> دخول كموظف المبيعات
            </button>
        </div>

        <div class="credentials">
            <h4><i class="fas fa-info-circle"></i> بيانات الدخول الصحيحة</h4>
            <div class="credential"><strong>مدير النظام:</strong> admin / admin123 / manager</div>
            <div class="credential"><strong>مشرف المبيعات:</strong> supervisor / super123 / supervisor</div>
            <div class="credential"><strong>موظف المبيعات:</strong> sales / sales123 / telesales</div>
        </div>
    </div>

    <script>
        // قاعدة بيانات المستخدمين الجديدة
        const USERS = [
            {
                id: 1,
                username: 'admin',
                password: 'admin123',
                role: 'manager',
                name: 'مدير النظام'
            },
            {
                id: 2,
                username: 'supervisor',
                password: 'super123',
                role: 'supervisor',
                name: 'مشرف المبيعات'
            },
            {
                id: 3,
                username: 'sales',
                password: 'sales123',
                role: 'telesales',
                name: 'موظف المبيعات الهاتفية'
            }
        ];

        let isLoggingIn = false;

        // عرض الرسائل
        function showMessage(text, type = 'loading', showSpinner = false) {
            const messageArea = document.getElementById('messageArea');
            let icon = '';
            
            switch(type) {
                case 'success':
                    icon = '<i class="fas fa-check-circle"></i> ';
                    break;
                case 'error':
                    icon = '<i class="fas fa-exclamation-triangle"></i> ';
                    break;
                case 'loading':
                    icon = showSpinner ? '<span class="spinner"></span> ' : '<i class="fas fa-hourglass-half"></i> ';
                    break;
            }
            
            messageArea.innerHTML = `<div class="message ${type}">${icon}${text}</div>`;
        }

        // تعطيل/تفعيل الأزرار
        function setButtonsDisabled(disabled) {
            document.getElementById('loginBtn').disabled = disabled;
            document.getElementById('username').disabled = disabled;
            document.getElementById('password').disabled = disabled;
            document.getElementById('role').disabled = disabled;
            document.querySelectorAll('.quick-btn').forEach(btn => btn.disabled = disabled);
        }

        // التحقق من بيانات المستخدم
        function validateUser(username, password, role) {
            const user = USERS.find(u => 
                u.username === username && 
                u.password === password && 
                u.role === role
            );
            return user;
        }

        // عملية تسجيل الدخول
        async function login(username, password, role) {
            if (isLoggingIn) return;
            
            try {
                isLoggingIn = true;
                setButtonsDisabled(true);
                showMessage('جاري التحقق من بيانات الدخول...', 'loading', true);

                // تأخير للمحاكاة
                await new Promise(resolve => setTimeout(resolve, 2000));

                // التحقق من البيانات
                const user = validateUser(username, password, role);

                if (user) {
                    // إنشاء الجلسة
                    const session = {
                        user: user,
                        loginTime: new Date().toISOString(),
                        sessionId: 'session_' + Date.now()
                    };

                    // حفظ الجلسة
                    localStorage.setItem('userSession', JSON.stringify(session));
                    localStorage.setItem('isLoggedIn', 'true');

                    showMessage(`مرحباً ${user.name}! جاري التحويل إلى لوحة التحكم...`, 'success');

                    // التحويل
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 2500);

                } else {
                    showMessage('بيانات الدخول غير صحيحة. يرجى التحقق من اسم المستخدم وكلمة المرور والدور.', 'error');
                }

            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                showMessage('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.', 'error');
            } finally {
                isLoggingIn = false;
                setButtonsDisabled(false);
            }
        }

        // الدخول السريع
        function quickLogin(username, password, role) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            document.getElementById('role').value = role;
            
            login(username, password, role);
        }

        // معالج النموذج
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const role = document.getElementById('role').value;

            if (!username || !password || !role) {
                showMessage('يرجى ملء جميع الحقول المطلوبة.', 'error');
                return;
            }

            login(username, password, role);
        });

        // فحص الجلسة الموجودة
        function checkSession() {
            try {
                const isLoggedIn = localStorage.getItem('isLoggedIn');
                const sessionData = localStorage.getItem('userSession');

                if (isLoggedIn === 'true' && sessionData) {
                    const session = JSON.parse(sessionData);
                    if (session.user && session.user.name) {
                        showMessage(`مرحباً ${session.user.name}! أنت مسجل دخول بالفعل. جاري التحويل...`, 'success');
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 2000);
                        return;
                    }
                }

                // مسح البيانات غير الصحيحة
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('userSession');
            } catch (error) {
                console.error('خطأ في فحص الجلسة:', error);
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('userSession');
            }
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('نظام تسجيل الدخول الجديد جاهز');
            console.log('المستخدمون المتاحون:', USERS.map(u => `${u.username}/${u.role}`));
            checkSession();
        });

        // معالج الأخطاء
        window.addEventListener('error', function(e) {
            console.error('خطأ عام:', e.error);
            showMessage('حدث خطأ في النظام. يرجى إعادة تحميل الصفحة.', 'error');
        });
    </script>
</body>
</html>
