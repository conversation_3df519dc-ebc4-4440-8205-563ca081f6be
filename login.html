<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام CRM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            width: 100%;
            max-width: 450px;
            text-align: center;
        }

        .logo {
            margin-bottom: 2rem;
        }

        .logo i {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .logo h1 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }

        .logo p {
            color: #666;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 600;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .quick-login {
            margin-top: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }

        .quick-login h3 {
            color: #495057;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .quick-btn {
            width: 100%;
            background: #28a745;
            color: white;
            border: none;
            padding: 0.8rem;
            border-radius: 8px;
            margin: 0.5rem 0;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 0.95rem;
        }

        .quick-btn:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        .message {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 10px;
            text-align: center;
            font-weight: 500;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .credentials-info {
            background: #e9ecef;
            padding: 1rem;
            border-radius: 10px;
            margin-top: 1rem;
            font-size: 0.9rem;
            text-align: right;
        }

        .credentials-info h4 {
            color: #495057;
            margin-bottom: 0.5rem;
            text-align: center;
        }

        .credential-row {
            margin: 0.3rem 0;
            color: #6c757d;
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 2rem;
                margin: 10px;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <i class="fas fa-users-cog"></i>
            <h1>نظام إدارة علاقات العملاء</h1>
            <p>مرحباً بك في نظام CRM</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" required>
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <div class="form-group">
                <label for="role">الدور الوظيفي:</label>
                <select id="role" name="role" required>
                    <option value="">-- اختر الدور --</option>
                    <option value="manager">مدير النظام</option>
                    <option value="supervisor">مشرف المبيعات</option>
                    <option value="telesales">موظف المبيعات</option>
                </select>
            </div>

            <button type="submit" class="login-btn" id="loginButton">
                <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
            </button>
        </form>

        <div id="messageArea"></div>

        <div class="quick-login">
            <h3><i class="fas fa-bolt"></i> دخول سريع للاختبار</h3>
            <button type="button" class="quick-btn" onclick="quickLogin('admin', 'admin123', 'manager')">
                <i class="fas fa-crown"></i> دخول كمدير النظام
            </button>
            <button type="button" class="quick-btn" onclick="quickLogin('supervisor', 'super123', 'supervisor')">
                <i class="fas fa-user-tie"></i> دخول كمشرف المبيعات
            </button>
            <button type="button" class="quick-btn" onclick="quickLogin('sales', 'sales123', 'telesales')">
                <i class="fas fa-headset"></i> دخول كموظف المبيعات
            </button>
        </div>

        <div class="credentials-info">
            <h4><i class="fas fa-key"></i> بيانات الدخول الصحيحة</h4>
            <div class="credential-row"><strong>مدير النظام:</strong> admin / admin123 / manager</div>
            <div class="credential-row"><strong>مشرف المبيعات:</strong> supervisor / super123 / supervisor</div>
            <div class="credential-row"><strong>موظف المبيعات:</strong> sales / sales123 / telesales</div>
        </div>
    </div>

    <script>
        // نظام المصادقة الجديد - بسيط ومضمون
        const USERS = [
            {
                username: 'admin',
                password: 'admin123',
                role: 'manager',
                name: 'مدير النظام',
                id: 1
            },
            {
                username: 'supervisor',
                password: 'super123',
                role: 'supervisor',
                name: 'مشرف المبيعات',
                id: 2
            },
            {
                username: 'sales',
                password: 'sales123',
                role: 'telesales',
                name: 'موظف المبيعات',
                id: 3
            }
        ];

        let isLoggingIn = false;

        // عرض الرسائل
        function showMessage(text, type = 'loading') {
            const messageArea = document.getElementById('messageArea');
            let icon = '';
            
            switch(type) {
                case 'success':
                    icon = '<i class="fas fa-check-circle"></i> ';
                    break;
                case 'error':
                    icon = '<i class="fas fa-exclamation-circle"></i> ';
                    break;
                case 'loading':
                    icon = '<span class="spinner"></span> ';
                    break;
            }
            
            messageArea.innerHTML = `<div class="message ${type}">${icon}${text}</div>`;
        }

        // تعطيل/تفعيل الأزرار
        function setButtonsDisabled(disabled) {
            document.getElementById('loginButton').disabled = disabled;
            document.querySelectorAll('.quick-btn').forEach(btn => btn.disabled = disabled);
        }

        // التحقق من صحة بيانات الدخول
        function validateCredentials(username, password, role) {
            // البحث عن المستخدم
            const user = USERS.find(u => 
                u.username === username && 
                u.password === password && 
                u.role === role
            );
            
            return user || null;
        }

        // تسجيل الدخول
        async function performLogin(username, password, role) {
            if (isLoggingIn) return;
            
            isLoggingIn = true;
            setButtonsDisabled(true);
            showMessage('جاري التحقق من بيانات الدخول...', 'loading');

            try {
                // محاكاة تأخير الشبكة
                await new Promise(resolve => setTimeout(resolve, 1500));

                // التحقق من البيانات
                const user = validateCredentials(username, password, role);

                if (user) {
                    // حفظ بيانات الجلسة
                    const sessionData = {
                        user: user,
                        loginTime: new Date().toISOString(),
                        sessionId: 'session_' + Date.now()
                    };

                    localStorage.setItem('userSession', JSON.stringify(sessionData));
                    localStorage.setItem('isLoggedIn', 'true');

                    showMessage(`مرحباً ${user.name}! جاري التحويل إلى لوحة التحكم...`, 'success');

                    // التحويل إلى لوحة التحكم
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 2000);

                } else {
                    showMessage('بيانات الدخول غير صحيحة. يرجى التحقق من اسم المستخدم وكلمة المرور والدور.', 'error');
                }

            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                showMessage('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.', 'error');
            } finally {
                isLoggingIn = false;
                setButtonsDisabled(false);
            }
        }

        // الدخول السريع
        function quickLogin(username, password, role) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            document.getElementById('role').value = role;
            
            performLogin(username, password, role);
        }

        // معالج إرسال النموذج
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const role = document.getElementById('role').value;

            if (!username || !password || !role) {
                showMessage('يرجى ملء جميع الحقول المطلوبة.', 'error');
                return;
            }

            performLogin(username, password, role);
        });

        // التحقق من الجلسة الموجودة
        function checkExistingSession() {
            try {
                const isLoggedIn = localStorage.getItem('isLoggedIn');
                const sessionData = localStorage.getItem('userSession');

                if (isLoggedIn === 'true' && sessionData) {
                    const session = JSON.parse(sessionData);
                    if (session.user && session.user.name) {
                        showMessage(`مرحباً ${session.user.name}! أنت مسجل دخول بالفعل. جاري التحويل...`, 'success');
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 2000);
                        return;
                    }
                }

                // مسح البيانات غير الصحيحة
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('userSession');
            } catch (error) {
                console.error('خطأ في فحص الجلسة:', error);
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('userSession');
            }
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('نظام تسجيل الدخول الجديد جاهز');
            console.log('المستخدمون المتاحون:', USERS.map(u => `${u.username}/${u.role}`));
            checkExistingSession();
        });

        // معالج الأخطاء العام
        window.addEventListener('error', function(e) {
            console.error('خطأ عام:', e.error);
            showMessage('حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.', 'error');
        });
    </script>
</body>
</html>
