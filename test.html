<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - CRM Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار نظام إدارة علاقات العملاء</h1>
        
        <div class="test-section">
            <h2>1. اختبار تحميل الملفات</h2>
            <button onclick="testFileLoading()">اختبار تحميل الملفات</button>
            <div id="fileLoadingResult"></div>
        </div>
        
        <div class="test-section">
            <h2>2. اختبار نظام التخزين</h2>
            <button onclick="testStorage()">اختبار التخزين</button>
            <div id="storageResult"></div>
        </div>
        
        <div class="test-section">
            <h2>3. اختبار نظام المصادقة</h2>
            <button onclick="testAuthentication()">اختبار المصادقة</button>
            <div id="authResult"></div>
        </div>
        
        <div class="test-section">
            <h2>4. اختبار البيانات التجريبية</h2>
            <button onclick="testDemoData()">اختبار البيانات</button>
            <div id="demoDataResult"></div>
        </div>
        
        <div class="test-section">
            <h2>5. اختبار تسجيل الدخول</h2>
            <button onclick="testLogin()">اختبار تسجيل الدخول</button>
            <div id="loginResult"></div>
        </div>
        
        <div class="test-section">
            <h2>6. عرض البيانات المحفوظة</h2>
            <button onclick="showStoredData()">عرض البيانات المحفوظة</button>
            <div id="storedDataResult"></div>
        </div>

        <div class="test-section">
            <h2>7. إعادة تعيين البيانات</h2>
            <button onclick="resetData()" style="background: #dc3545;">إعادة تعيين البيانات</button>
            <div id="resetResult"></div>
        </div>
    </div>

    <!-- Load CRM Scripts -->
    <script src="js/language.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>

    <script>
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }

        function testFileLoading() {
            let results = [];
            
            // Test if functions exist
            const functions = [
                'initializeLanguage',
                'initializeStorage', 
                'getStorageManager',
                'initializeAuth',
                'login',
                'getCurrentUser'
            ];
            
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    results.push(`✅ ${func} محمل بنجاح`);
                } else {
                    results.push(`❌ ${func} غير محمل`);
                }
            });
            
            showResult('fileLoadingResult', results.join('<br>'), 'info');
        }

        function testStorage() {
            try {
                const storage = getStorageManager();
                const users = storage.getUsers();
                const customers = storage.getCustomers();
                const suppliers = storage.getSuppliers();
                const telesales = storage.getTelesales();
                
                const results = [
                    `✅ نظام التخزين يعمل بنجاح`,
                    `📊 عدد المستخدمين: ${users.length}`,
                    `👥 عدد العملاء: ${customers.length}`,
                    `🚚 عدد الموردين: ${suppliers.length}`,
                    `📞 عدد موظفي المبيعات: ${telesales.length}`
                ];
                
                showResult('storageResult', results.join('<br>'), 'success');
            } catch (error) {
                showResult('storageResult', `❌ خطأ في نظام التخزين: ${error.message}`, 'error');
            }
        }

        function testAuthentication() {
            try {
                initializeAuth();
                showResult('authResult', '✅ نظام المصادقة تم تهيئته بنجاح', 'success');
            } catch (error) {
                showResult('authResult', `❌ خطأ في نظام المصادقة: ${error.message}`, 'error');
            }
        }

        function testDemoData() {
            try {
                const storage = getStorageManager();
                const users = storage.getUsers();
                
                let results = ['<h4>المستخدمون المتاحون:</h4>'];
                users.forEach(user => {
                    results.push(`👤 ${user.username} (${user.role}) - ${user.active ? 'نشط' : 'غير نشط'}`);
                });
                
                showResult('demoDataResult', results.join('<br>'), 'info');
            } catch (error) {
                showResult('demoDataResult', `❌ خطأ في البيانات التجريبية: ${error.message}`, 'error');
            }
        }

        function testLogin() {
            try {
                // Test all three accounts
                const testAccounts = [
                    { username: 'admin', password: 'admin123', role: 'manager', name: 'مدير' },
                    { username: 'supervisor', password: 'super123', role: 'supervisor', name: 'مشرف' },
                    { username: 'sales', password: 'sales123', role: 'telesales', name: 'مبيعات' }
                ];

                let results = ['<h4>نتائج اختبار تسجيل الدخول:</h4>'];

                testAccounts.forEach(account => {
                    try {
                        const loginResult = login(account.username, account.password, account.role);

                        if (loginResult && loginResult.success) {
                            results.push(`✅ ${account.name}: نجح تسجيل الدخول`);
                            // Logout after successful login
                            logout();
                        } else {
                            const message = loginResult ? loginResult.message : 'خطأ غير معروف';
                            results.push(`❌ ${account.name}: فشل - ${message}`);
                        }
                    } catch (error) {
                        results.push(`❌ ${account.name}: خطأ - ${error.message}`);
                    }
                });

                showResult('loginResult', results.join('<br>'), 'info');
            } catch (error) {
                showResult('loginResult', `❌ خطأ في اختبار تسجيل الدخول: ${error.message}`, 'error');
            }
        }

        function showStoredData() {
            try {
                const storage = getStorageManager();
                const users = storage.getUsers();

                let results = ['<h4>البيانات المحفوظة في localStorage:</h4>'];

                // Show localStorage keys
                results.push('<h5>مفاتيح localStorage:</h5>');
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    results.push(`🔑 ${key}`);
                }

                // Show users in detail
                results.push('<h5>المستخدمون المحفوظون:</h5>');
                if (users.length === 0) {
                    results.push('❌ لا يوجد مستخدمون محفوظون');
                } else {
                    users.forEach((user, index) => {
                        results.push(`👤 ${index + 1}. ${user.username} | ${user.role} | ${user.active ? 'نشط' : 'غير نشط'}`);
                    });
                }

                // Show raw localStorage data for users
                const rawUsers = localStorage.getItem('crm_users');
                if (rawUsers) {
                    results.push('<h5>البيانات الخام للمستخدمين:</h5>');
                    results.push(`<pre>${rawUsers}</pre>`);
                }

                showResult('storedDataResult', results.join('<br>'), 'info');
            } catch (error) {
                showResult('storedDataResult', `❌ خطأ في عرض البيانات: ${error.message}`, 'error');
            }
        }

        function resetData() {
            try {
                localStorage.clear();
                showResult('resetResult', '✅ تم مسح جميع البيانات. يرجى إعادة تحميل الصفحة.', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } catch (error) {
                showResult('resetResult', `❌ خطأ في إعادة تعيين البيانات: ${error.message}`, 'error');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            try {
                initializeLanguage();
                const storage = getStorageManager();
                initializeAuth();
                console.log('Test page initialized successfully');
            } catch (error) {
                console.error('Test page initialization error:', error);
            }
        });
    </script>
</body>
</html>
