// Telesales Team Management Module for CRM
class TelesalesManager {
    constructor() {
        this.currentTelesales = [];
        this.currentTasks = [];
        this.filteredTelesales = [];
        this.filteredTasks = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortField = 'employeeName';
        this.sortDirection = 'asc';
        this.filters = {};
        this.activeTab = 'employees';

        this.init();
    }

    init() {
        this.loadData();
        this.bindEvents();
    }

    bindEvents() {
        // Listen for module navigation
        document.addEventListener('navigateToModule', (e) => {
            if (e.detail.module === 'telesales') {
                this.handleNavigation(e.detail);
            }
        });
    }

    handleNavigation(detail) {
        if (detail.action === 'view' && detail.id) {
            if (detail.type === 'tasks') {
                this.activeTab = 'tasks';
                this.viewTask(detail.id);
            } else {
                this.viewEmployee(detail.id);
            }
        } else {
            this.showTelesalesModule();
        }
    }

    loadData() {
        const storage = getStorageManager();
        this.currentTelesales = storage.getTelesales();
        this.currentTasks = storage.getTasks();
        this.filteredTelesales = [...this.currentTelesales];
        this.filteredTasks = [...this.currentTasks];
        this.applyFiltersAndSort();
    }

    showTelesalesModule() {
        const moduleContent = this.createTelesalesModule();
        const telesalesModule = document.getElementById('telesalesModule');

        if (telesalesModule) {
            telesalesModule.innerHTML = moduleContent;
            this.bindModuleEvents();
            this.showTab(this.activeTab);
            this.updateStatistics();
        }
    }

    createTelesalesModule() {
        return `
            <div class="module-header">
                <h2 data-key="telesales_management">${getTranslation('telesales_management')}</h2>
                <div class="module-actions">
                    <button class="btn btn-primary" id="addEmployeeBtn" data-permission="telesales:create">
                        <i class="fas fa-user-plus"></i>
                        <span data-key="add_employee">${getTranslation('add_employee')}</span>
                    </button>
                    <button class="btn btn-success" id="assignTaskBtn" data-permission="tasks:assign">
                        <i class="fas fa-tasks"></i>
                        <span data-key="assign_task">${getTranslation('assign_task')}</span>
                    </button>
                    <button class="btn btn-secondary" id="exportTelesalesBtn" data-permission="telesales:export">
                        <i class="fas fa-download"></i>
                        <span data-key="export">${getTranslation('export')}</span>
                    </button>
                </div>
            </div>

            <div class="telesales-content">
                <!-- Statistics Cards -->
                <div class="stats-row">
                    <div class="stat-card">
                        <div class="stat-icon telesales">
                            <i class="fas fa-headset"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalEmployeesCount">0</h3>
                            <p data-key="total_employees">${getTranslation('total_employees')}</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon tasks">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalTasksCount">0</h3>
                            <p data-key="total_tasks">${getTranslation('total_tasks')}</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon pending">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="pendingTasksCount">0</h3>
                            <p data-key="pending_tasks">${getTranslation('pending_tasks')}</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon completed">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="completedTasksCount">0</h3>
                            <p data-key="completed_tasks">${getTranslation('completed_tasks')}</p>
                        </div>
                    </div>
                </div>

                <!-- Tab Navigation -->
                <div class="tab-navigation">
                    <button class="tab-btn active" data-tab="employees">
                        <i class="fas fa-users"></i>
                        <span data-key="employees">${getTranslation('employees')}</span>
                    </button>
                    <button class="tab-btn" data-tab="tasks">
                        <i class="fas fa-tasks"></i>
                        <span data-key="tasks">${getTranslation('tasks')}</span>
                    </button>
                    <button class="tab-btn" data-tab="performance">
                        <i class="fas fa-chart-line"></i>
                        <span data-key="performance">${getTranslation('performance')}</span>
                    </button>
                </div>

                <!-- Employees Tab -->
                <div class="tab-content" id="employeesTab">
                    <!-- Filters and Search -->
                    <div class="filters-section">
                        <div class="filters-row">
                            <div class="filter-group">
                                <label data-key="search">${getTranslation('search')}</label>
                                <div class="search-input">
                                    <i class="fas fa-search"></i>
                                    <input type="text" id="employeeSearch" placeholder="${getTranslation('search_employees')}" data-key-placeholder="search_employees">
                                </div>
                            </div>

                            <div class="filter-group">
                                <label data-key="position">${getTranslation('position')}</label>
                                <select id="positionFilter">
                                    <option value="">${getTranslation('all_positions')}</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label data-key="department">${getTranslation('department')}</label>
                                <select id="departmentFilter">
                                    <option value="">${getTranslation('all_departments')}</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <button class="btn btn-secondary" id="clearEmployeeFiltersBtn">
                                    <i class="fas fa-times"></i>
                                    <span data-key="clear_filters">${getTranslation('clear_filters')}</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Employees Table -->
                    <div class="table-container">
                        <div class="table-header">
                            <div class="table-info">
                                <span id="employeeTableInfo">${getTranslation('showing')} 0 ${getTranslation('of')} 0 ${getTranslation('entries')}</span>
                            </div>
                            <div class="table-controls">
                                <div class="page-size-selector">
                                    <label data-key="show">${getTranslation('show')}</label>
                                    <select id="employeePageSizeSelect">
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                    </select>
                                    <span data-key="entries">${getTranslation('entries')}</span>
                                </div>
                            </div>
                        </div>

                        <div class="table-wrapper">
                            <table class="data-table" id="employeesTable">
                                <thead>
                                    <tr>
                                        <th data-sort="employeeName" class="sortable">
                                            <span data-key="employee_name">${getTranslation('employee_name')}</span>
                                            <i class="fas fa-sort"></i>
                                        </th>
                                        <th data-sort="employeeId" class="sortable">
                                            <span data-key="employee_id">${getTranslation('employee_id')}</span>
                                            <i class="fas fa-sort"></i>
                                        </th>
                                        <th data-sort="position" class="sortable">
                                            <span data-key="position">${getTranslation('position')}</span>
                                            <i class="fas fa-sort"></i>
                                        </th>
                                        <th data-sort="department" class="sortable">
                                            <span data-key="department">${getTranslation('department')}</span>
                                            <i class="fas fa-sort"></i>
                                        </th>
                                        <th data-sort="hireDate" class="sortable">
                                            <span data-key="hire_date">${getTranslation('hire_date')}</span>
                                            <i class="fas fa-sort"></i>
                                        </th>
                                        <th>
                                            <span data-key="active_tasks">${getTranslation('active_tasks')}</span>
                                        </th>
                                        <th class="actions-column">
                                            <span data-key="actions">${getTranslation('actions')}</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="employeesTableBody">
                                    <!-- Table rows will be inserted here -->
                                </tbody>
                            </table>
                        </div>

                        <div class="table-footer">
                            <div class="pagination" id="employeesPagination">
                                <!-- Pagination will be inserted here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tasks Tab -->
                <div class="tab-content" id="tasksTab" style="display: none;">
                    <!-- Task Filters -->
                    <div class="filters-section">
                        <div class="filters-row">
                            <div class="filter-group">
                                <label data-key="search">${getTranslation('search')}</label>
                                <div class="search-input">
                                    <i class="fas fa-search"></i>
                                    <input type="text" id="taskSearch" placeholder="${getTranslation('search_tasks')}" data-key-placeholder="search_tasks">
                                </div>
                            </div>

                            <div class="filter-group">
                                <label data-key="assigned_to">${getTranslation('assigned_to')}</label>
                                <select id="assignedToFilter">
                                    <option value="">${getTranslation('all_employees')}</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label data-key="status">${getTranslation('status')}</label>
                                <select id="taskStatusFilter">
                                    <option value="">${getTranslation('all_statuses')}</option>
                                    <option value="pending">${getTranslation('pending')}</option>
                                    <option value="in_progress">${getTranslation('in_progress')}</option>
                                    <option value="completed">${getTranslation('completed')}</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label data-key="priority">${getTranslation('priority')}</label>
                                <select id="priorityFilter">
                                    <option value="">${getTranslation('all_priorities')}</option>
                                    <option value="high">${getTranslation('high')}</option>
                                    <option value="medium">${getTranslation('medium')}</option>
                                    <option value="low">${getTranslation('low')}</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <button class="btn btn-secondary" id="clearTaskFiltersBtn">
                                    <i class="fas fa-times"></i>
                                    <span data-key="clear_filters">${getTranslation('clear_filters')}</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Tasks Table -->
                    <div class="table-container">
                        <div class="table-header">
                            <div class="table-info">
                                <span id="taskTableInfo">${getTranslation('showing')} 0 ${getTranslation('of')} 0 ${getTranslation('entries')}</span>
                            </div>
                            <div class="table-controls">
                                <div class="page-size-selector">
                                    <label data-key="show">${getTranslation('show')}</label>
                                    <select id="taskPageSizeSelect">
                                        <option value="10">10</option>
                                        <option value="25">25</option>
                                        <option value="50">50</option>
                                    </select>
                                    <span data-key="entries">${getTranslation('entries')}</span>
                                </div>
                            </div>
                        </div>

                        <div class="table-wrapper">
                            <table class="data-table" id="tasksTable">
                                <thead>
                                    <tr>
                                        <th data-sort="taskTitle" class="sortable">
                                            <span data-key="task_title">${getTranslation('task_title')}</span>
                                            <i class="fas fa-sort"></i>
                                        </th>
                                        <th data-sort="assignedTo" class="sortable">
                                            <span data-key="assigned_to">${getTranslation('assigned_to')}</span>
                                            <i class="fas fa-sort"></i>
                                        </th>
                                        <th data-sort="priority" class="sortable">
                                            <span data-key="priority">${getTranslation('priority')}</span>
                                            <i class="fas fa-sort"></i>
                                        </th>
                                        <th data-sort="status" class="sortable">
                                            <span data-key="status">${getTranslation('status')}</span>
                                            <i class="fas fa-sort"></i>
                                        </th>
                                        <th data-sort="deadline" class="sortable">
                                            <span data-key="deadline">${getTranslation('deadline')}</span>
                                            <i class="fas fa-sort"></i>
                                        </th>
                                        <th data-sort="createdAt" class="sortable">
                                            <span data-key="created_date">${getTranslation('created_date')}</span>
                                            <i class="fas fa-sort"></i>
                                        </th>
                                        <th class="actions-column">
                                            <span data-key="actions">${getTranslation('actions')}</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="tasksTableBody">
                                    <!-- Table rows will be inserted here -->
                                </tbody>
                            </table>
                        </div>

                        <div class="table-footer">
                            <div class="pagination" id="tasksPagination">
                                <!-- Pagination will be inserted here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Tab -->
                <div class="tab-content" id="performanceTab" style="display: none;">
                    <div class="performance-content">
                        <div class="performance-grid">
                            <div class="performance-widget">
                                <h3 data-key="weekly_performance">${getTranslation('weekly_performance')}</h3>
                                <div id="weeklyPerformanceChart" class="chart-container">
                                    <!-- Performance chart will be rendered here -->
                                </div>
                            </div>

                            <div class="performance-widget">
                                <h3 data-key="task_completion_rate">${getTranslation('task_completion_rate')}</h3>
                                <div id="taskCompletionChart" class="chart-container">
                                    <!-- Task completion chart will be rendered here -->
                                </div>
                            </div>

                            <div class="performance-widget">
                                <h3 data-key="employee_rankings">${getTranslation('employee_rankings')}</h3>
                                <div id="employeeRankings" class="rankings-container">
                                    <!-- Employee rankings will be rendered here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindModuleEvents() {
        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const tab = btn.getAttribute('data-tab');
                this.showTab(tab);
            });
        });

        // Add employee button
        const addBtn = document.getElementById('addEmployeeBtn');
        if (addBtn && hasPermission('telesales', 'create')) {
            addBtn.addEventListener('click', () => this.showAddEmployeeModal());
        }

        // Assign task button
        const assignTaskBtn = document.getElementById('assignTaskBtn');
        if (assignTaskBtn && hasPermission('tasks', 'assign')) {
            assignTaskBtn.addEventListener('click', () => this.showAssignTaskModal());
        }

        // Export button
        const exportBtn = document.getElementById('exportTelesalesBtn');
        if (exportBtn && hasPermission('telesales', 'export')) {
            exportBtn.addEventListener('click', () => this.exportTelesales());
        }

        // Employee search and filters
        const employeeSearch = document.getElementById('employeeSearch');
        if (employeeSearch) {
            employeeSearch.addEventListener('input', (e) => {
                this.filters.employeeSearch = e.target.value;
                this.applyFiltersAndSort();
                this.renderEmployeesTable();
            });
        }

        ['positionFilter', 'departmentFilter'].forEach(filterId => {
            const filterElement = document.getElementById(filterId);
            if (filterElement) {
                filterElement.addEventListener('change', (e) => {
                    const filterType = filterId.replace('Filter', '');
                    this.filters[filterType] = e.target.value;
                    this.applyFiltersAndSort();
                    this.renderEmployeesTable();
                });
            }
        });

        // Task search and filters
        const taskSearch = document.getElementById('taskSearch');
        if (taskSearch) {
            taskSearch.addEventListener('input', (e) => {
                this.filters.taskSearch = e.target.value;
                this.applyTaskFiltersAndSort();
                this.renderTasksTable();
            });
        }

        ['assignedToFilter', 'taskStatusFilter', 'priorityFilter'].forEach(filterId => {
            const filterElement = document.getElementById(filterId);
            if (filterElement) {
                filterElement.addEventListener('change', (e) => {
                    const filterType = filterId.replace('Filter', '');
                    this.filters[filterType] = e.target.value;
                    this.applyTaskFiltersAndSort();
                    this.renderTasksTable();
                });
            }
        });

        // Clear filters buttons
        const clearEmployeeFiltersBtn = document.getElementById('clearEmployeeFiltersBtn');
        if (clearEmployeeFiltersBtn) {
            clearEmployeeFiltersBtn.addEventListener('click', () => this.clearEmployeeFilters());
        }

        const clearTaskFiltersBtn = document.getElementById('clearTaskFiltersBtn');
        if (clearTaskFiltersBtn) {
            clearTaskFiltersBtn.addEventListener('click', () => this.clearTaskFilters());
        }

        // Page size selectors
        const employeePageSizeSelect = document.getElementById('employeePageSizeSelect');
        if (employeePageSizeSelect) {
            employeePageSizeSelect.value = this.pageSize;
            employeePageSizeSelect.addEventListener('change', (e) => {
                this.pageSize = parseInt(e.target.value);
                this.currentPage = 1;
                this.renderEmployeesTable();
            });
        }

        const taskPageSizeSelect = document.getElementById('taskPageSizeSelect');
        if (taskPageSizeSelect) {
            taskPageSizeSelect.value = this.pageSize;
            taskPageSizeSelect.addEventListener('change', (e) => {
                this.pageSize = parseInt(e.target.value);
                this.currentPage = 1;
                this.renderTasksTable();
            });
        }

        // Table sorting
        const employeeSortableHeaders = document.querySelectorAll('#employeesTable th.sortable');
        employeeSortableHeaders.forEach(header => {
            header.addEventListener('click', () => {
                const field = header.getAttribute('data-sort');
                this.handleEmployeeSort(field);
            });
        });

        const taskSortableHeaders = document.querySelectorAll('#tasksTable th.sortable');
        taskSortableHeaders.forEach(header => {
            header.addEventListener('click', () => {
                const field = header.getAttribute('data-sort');
                this.handleTaskSort(field);
            });
        });
    }

    showTab(tabName) {
        // Update active tab button
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Show/hide tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.style.display = 'none';
        });
        document.getElementById(`${tabName}Tab`).style.display = 'block';

        this.activeTab = tabName;

        // Load tab-specific content
        switch (tabName) {
            case 'employees':
                this.renderEmployeesTable();
                break;
            case 'tasks':
                this.renderTasksTable();
                break;
            case 'performance':
                this.renderPerformanceData();
                break;
        }
    }

    applyFiltersAndSort() {
        let filtered = [...this.currentTelesales];

        // Apply search filter
        if (this.filters.employeeSearch) {
            const searchTerm = this.filters.employeeSearch.toLowerCase();
            filtered = filtered.filter(employee =>
                Object.values(employee).some(value =>
                    value && value.toString().toLowerCase().includes(searchTerm)
                )
            );
        }

        // Apply position filter
        if (this.filters.position) {
            filtered = filtered.filter(employee => employee.position === this.filters.position);
        }

        // Apply department filter
        if (this.filters.department) {
            filtered = filtered.filter(employee => employee.department === this.filters.department);
        }

        // Apply sorting
        filtered.sort((a, b) => {
            const aValue = a[this.sortField] || '';
            const bValue = b[this.sortField] || '';

            if (this.sortDirection === 'asc') {
                return aValue.toString().localeCompare(bValue.toString());
            } else {
                return bValue.toString().localeCompare(aValue.toString());
            }
        });

        this.filteredTelesales = filtered;
        this.currentPage = 1;
    }

    applyTaskFiltersAndSort() {
        let filtered = [...this.currentTasks];

        // Apply search filter
        if (this.filters.taskSearch) {
            const searchTerm = this.filters.taskSearch.toLowerCase();
            filtered = filtered.filter(task =>
                Object.values(task).some(value =>
                    value && value.toString().toLowerCase().includes(searchTerm)
                )
            );
        }

        // Apply assigned to filter
        if (this.filters.assignedTo) {
            filtered = filtered.filter(task => task.assignedTo === this.filters.assignedTo);
        }

        // Apply status filter
        if (this.filters.taskStatus) {
            filtered = filtered.filter(task => task.status === this.filters.taskStatus);
        }

        // Apply priority filter
        if (this.filters.priority) {
            filtered = filtered.filter(task => task.priority === this.filters.priority);
        }

        // Apply sorting
        filtered.sort((a, b) => {
            const aValue = a[this.sortField] || '';
            const bValue = b[this.sortField] || '';

            if (this.sortDirection === 'asc') {
                return aValue.toString().localeCompare(bValue.toString());
            } else {
                return bValue.toString().localeCompare(aValue.toString());
            }
        });

        this.filteredTasks = filtered;
        this.currentPage = 1;
    }

    handleEmployeeSort(field) {
        if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortDirection = 'asc';
        }

        this.applyFiltersAndSort();
        this.renderEmployeesTable();
        this.updateEmployeeSortIcons();
    }

    handleTaskSort(field) {
        if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortDirection = 'asc';
        }

        this.applyTaskFiltersAndSort();
        this.renderTasksTable();
        this.updateTaskSortIcons();
    }

    updateEmployeeSortIcons() {
        document.querySelectorAll('#employeesTable th.sortable i').forEach(icon => {
            icon.className = 'fas fa-sort';
        });

        const activeHeader = document.querySelector(`#employeesTable th[data-sort="${this.sortField}"] i`);
        if (activeHeader) {
            activeHeader.className = `fas fa-sort-${this.sortDirection === 'asc' ? 'up' : 'down'}`;
        }
    }

    updateTaskSortIcons() {
        document.querySelectorAll('#tasksTable th.sortable i').forEach(icon => {
            icon.className = 'fas fa-sort';
        });

        const activeHeader = document.querySelector(`#tasksTable th[data-sort="${this.sortField}"] i`);
        if (activeHeader) {
            activeHeader.className = `fas fa-sort-${this.sortDirection === 'asc' ? 'up' : 'down'}`;
        }
    }

    renderEmployeesTable() {
        const tbody = document.getElementById('employeesTableBody');
        if (!tbody) return;

        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageEmployees = this.filteredTelesales.slice(startIndex, endIndex);

        tbody.innerHTML = pageEmployees.map(employee => this.createEmployeeRow(employee)).join('');

        this.updateEmployeeTableInfo();
        this.renderEmployeesPagination();
        this.bindEmployeeRowEvents();
        this.updateEmployeeFilterOptions();
    }

    renderTasksTable() {
        const tbody = document.getElementById('tasksTableBody');
        if (!tbody) return;

        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageTasks = this.filteredTasks.slice(startIndex, endIndex);

        tbody.innerHTML = pageTasks.map(task => this.createTaskRow(task)).join('');

        this.updateTaskTableInfo();
        this.renderTasksPagination();
        this.bindTaskRowEvents();
        this.updateTaskFilterOptions();
    }

    createEmployeeRow(employee) {
        const activeTasks = this.currentTasks.filter(task =>
            task.assignedTo === employee.id && task.status !== 'completed'
        ).length;

        const hireDate = employee.hireDate ?
            new Date(employee.hireDate).toLocaleDateString() : '-';

        return `
            <tr data-employee-id="${employee.id}">
                <td>
                    <div class="employee-info">
                        <div class="employee-name">${this.escapeHtml(employee.employeeName || '')}</div>
                        ${employee.email ? `<div class="employee-email">${this.escapeHtml(employee.email)}</div>` : ''}
                    </div>
                </td>
                <td>${this.escapeHtml(employee.employeeId || '')}</td>
                <td>${this.escapeHtml(employee.position || '')}</td>
                <td>${this.escapeHtml(employee.department || '')}</td>
                <td>${hireDate}</td>
                <td>
                    <span class="task-count-badge">${activeTasks}</span>
                </td>
                <td class="actions-cell">
                    <div class="action-buttons">
                        <button class="btn-action btn-view" onclick="telesalesManager.viewEmployee('${employee.id}')" title="${getTranslation('view')}">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${hasPermission('telesales', 'update') ? `
                            <button class="btn-action btn-edit" onclick="telesalesManager.editEmployee('${employee.id}')" title="${getTranslation('edit')}">
                                <i class="fas fa-edit"></i>
                            </button>
                        ` : ''}
                        ${hasPermission('tasks', 'assign') ? `
                            <button class="btn-action btn-assign" onclick="telesalesManager.assignTaskToEmployee('${employee.id}')" title="${getTranslation('assign_task')}">
                                <i class="fas fa-tasks"></i>
                            </button>
                        ` : ''}
                        ${hasPermission('telesales', 'delete') ? `
                            <button class="btn-action btn-delete" onclick="telesalesManager.deleteEmployee('${employee.id}')" title="${getTranslation('delete')}">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    }

    createTaskRow(task) {
        const assignedEmployee = this.currentTelesales.find(emp => emp.id === task.assignedTo);
        const assignedName = assignedEmployee ? assignedEmployee.employeeName : task.assignedTo;

        const deadline = task.deadline ?
            new Date(task.deadline).toLocaleDateString() : '-';
        const createdDate = task.createdAt ?
            new Date(task.createdAt).toLocaleDateString() : '-';

        const priorityClass = task.priority || 'medium';
        const statusClass = task.status || 'pending';

        return `
            <tr data-task-id="${task.id}">
                <td>
                    <div class="task-info">
                        <div class="task-title">${this.escapeHtml(task.taskTitle || task.title || '')}</div>
                        ${task.taskDescription ? `<div class="task-description">${this.escapeHtml(task.taskDescription.substring(0, 50))}${task.taskDescription.length > 50 ? '...' : ''}</div>` : ''}
                    </div>
                </td>
                <td>${this.escapeHtml(assignedName || '')}</td>
                <td>
                    <span class="priority-badge priority-${priorityClass}">${getTranslation(task.priority) || task.priority}</span>
                </td>
                <td>
                    <span class="status-badge status-${statusClass}">${getTranslation(task.status) || task.status}</span>
                </td>
                <td>${deadline}</td>
                <td>${createdDate}</td>
                <td class="actions-cell">
                    <div class="action-buttons">
                        <button class="btn-action btn-view" onclick="telesalesManager.viewTask('${task.id}')" title="${getTranslation('view')}">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${hasPermission('tasks', 'update') ? `
                            <button class="btn-action btn-edit" onclick="telesalesManager.editTask('${task.id}')" title="${getTranslation('edit')}">
                                <i class="fas fa-edit"></i>
                            </button>
                        ` : ''}
                        ${hasPermission('tasks', 'delete') ? `
                            <button class="btn-action btn-delete" onclick="telesalesManager.deleteTask('${task.id}')" title="${getTranslation('delete')}">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    }

    bindEmployeeRowEvents() {
        document.querySelectorAll('#employeesTableBody tr').forEach(row => {
            row.addEventListener('dblclick', () => {
                const employeeId = row.getAttribute('data-employee-id');
                this.viewEmployee(employeeId);
            });
        });
    }

    bindTaskRowEvents() {
        document.querySelectorAll('#tasksTableBody tr').forEach(row => {
            row.addEventListener('dblclick', () => {
                const taskId = row.getAttribute('data-task-id');
                this.viewTask(taskId);
            });
        });
    }

    updateStatistics() {
        const stats = {
            totalEmployees: this.currentTelesales.length,
            totalTasks: this.currentTasks.length,
            pendingTasks: this.currentTasks.filter(t => t.status === 'pending').length,
            completedTasks: this.currentTasks.filter(t => t.status === 'completed').length
        };

        document.getElementById('totalEmployeesCount').textContent = stats.totalEmployees;
        document.getElementById('totalTasksCount').textContent = stats.totalTasks;
        document.getElementById('pendingTasksCount').textContent = stats.pendingTasks;
        document.getElementById('completedTasksCount').textContent = stats.completedTasks;
    }

    updateEmployeeTableInfo() {
        const tableInfo = document.getElementById('employeeTableInfo');
        if (tableInfo) {
            const startIndex = (this.currentPage - 1) * this.pageSize + 1;
            const endIndex = Math.min(this.currentPage * this.pageSize, this.filteredTelesales.length);
            const total = this.filteredTelesales.length;

            tableInfo.textContent = `${getTranslation('showing')} ${startIndex}-${endIndex} ${getTranslation('of')} ${total} ${getTranslation('entries')}`;
        }
    }

    updateTaskTableInfo() {
        const tableInfo = document.getElementById('taskTableInfo');
        if (tableInfo) {
            const startIndex = (this.currentPage - 1) * this.pageSize + 1;
            const endIndex = Math.min(this.currentPage * this.pageSize, this.filteredTasks.length);
            const total = this.filteredTasks.length;

            tableInfo.textContent = `${getTranslation('showing')} ${startIndex}-${endIndex} ${getTranslation('of')} ${total} ${getTranslation('entries')}`;
        }
    }

    updateEmployeeFilterOptions() {
        // Update position filter options
        const positions = [...new Set(this.currentTelesales.map(e => e.position).filter(Boolean))].sort();
        const positionFilter = document.getElementById('positionFilter');
        if (positionFilter) {
            const currentValue = positionFilter.value;
            positionFilter.innerHTML = `<option value="">${getTranslation('all_positions')}</option>` +
                positions.map(position => `<option value="${position}">${position}</option>`).join('');
            positionFilter.value = currentValue;
        }

        // Update department filter options
        const departments = [...new Set(this.currentTelesales.map(e => e.department).filter(Boolean))].sort();
        const departmentFilter = document.getElementById('departmentFilter');
        if (departmentFilter) {
            const currentValue = departmentFilter.value;
            departmentFilter.innerHTML = `<option value="">${getTranslation('all_departments')}</option>` +
                departments.map(dept => `<option value="${dept}">${dept}</option>`).join('');
            departmentFilter.value = currentValue;
        }
    }

    updateTaskFilterOptions() {
        // Update assigned to filter options
        const assignedToFilter = document.getElementById('assignedToFilter');
        if (assignedToFilter) {
            const currentValue = assignedToFilter.value;
            assignedToFilter.innerHTML = `<option value="">${getTranslation('all_employees')}</option>` +
                this.currentTelesales.map(emp =>
                    `<option value="${emp.id}">${emp.employeeName}</option>`
                ).join('');
            assignedToFilter.value = currentValue;
        }
    }

    clearEmployeeFilters() {
        this.filters.employeeSearch = '';
        this.filters.position = '';
        this.filters.department = '';
        this.currentPage = 1;

        document.getElementById('employeeSearch').value = '';
        document.getElementById('positionFilter').value = '';
        document.getElementById('departmentFilter').value = '';

        this.applyFiltersAndSort();
        this.renderEmployeesTable();
    }

    clearTaskFilters() {
        this.filters.taskSearch = '';
        this.filters.assignedTo = '';
        this.filters.taskStatus = '';
        this.filters.priority = '';
        this.currentPage = 1;

        document.getElementById('taskSearch').value = '';
        document.getElementById('assignedToFilter').value = '';
        document.getElementById('taskStatusFilter').value = '';
        document.getElementById('priorityFilter').value = '';

        this.applyTaskFiltersAndSort();
        this.renderTasksTable();
    }

    renderEmployeesPagination() {
        // Implementation similar to customer pagination
        console.log('Render employees pagination');
    }

    renderTasksPagination() {
        // Implementation similar to customer pagination
        console.log('Render tasks pagination');
    }

    renderPerformanceData() {
        // Load performance charts and data
        this.loadWeeklyPerformance();
        this.loadTaskCompletionRate();
        this.loadEmployeeRankings();
    }

    loadWeeklyPerformance() {
        const container = document.getElementById('weeklyPerformanceChart');
        if (container) {
            // Simple performance display - in a real app, you'd use a charting library
            const performanceData = this.calculateWeeklyPerformance();
            container.innerHTML = `
                <div class="simple-chart">
                    ${performanceData.map(data => `
                        <div class="chart-item">
                            <div class="chart-label">${data.employee}</div>
                            <div class="chart-bar">
                                <div class="chart-fill" style="width: ${data.percentage}%"></div>
                            </div>
                            <div class="chart-value">${data.completed}/${data.total}</div>
                        </div>
                    `).join('')}
                </div>
            `;
        }
    }

    loadTaskCompletionRate() {
        const container = document.getElementById('taskCompletionChart');
        if (container) {
            const stats = {
                completed: this.currentTasks.filter(t => t.status === 'completed').length,
                pending: this.currentTasks.filter(t => t.status === 'pending').length,
                inProgress: this.currentTasks.filter(t => t.status === 'in_progress').length
            };

            const total = stats.completed + stats.pending + stats.inProgress;
            const completionRate = total > 0 ? Math.round((stats.completed / total) * 100) : 0;

            container.innerHTML = `
                <div class="completion-rate">
                    <div class="rate-circle">
                        <div class="rate-percentage">${completionRate}%</div>
                        <div class="rate-label">${getTranslation('completed')}</div>
                    </div>
                    <div class="rate-breakdown">
                        <div class="rate-item">
                            <span class="rate-color completed"></span>
                            <span>${getTranslation('completed')}: ${stats.completed}</span>
                        </div>
                        <div class="rate-item">
                            <span class="rate-color in-progress"></span>
                            <span>${getTranslation('in_progress')}: ${stats.inProgress}</span>
                        </div>
                        <div class="rate-item">
                            <span class="rate-color pending"></span>
                            <span>${getTranslation('pending')}: ${stats.pending}</span>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    loadEmployeeRankings() {
        const container = document.getElementById('employeeRankings');
        if (container) {
            const rankings = this.calculateEmployeeRankings();

            container.innerHTML = `
                <div class="rankings-list">
                    ${rankings.map((employee, index) => `
                        <div class="ranking-item">
                            <div class="ranking-position">#${index + 1}</div>
                            <div class="ranking-info">
                                <div class="ranking-name">${employee.name}</div>
                                <div class="ranking-stats">${employee.completedTasks} ${getTranslation('completed_tasks')}</div>
                            </div>
                            <div class="ranking-score">${employee.score}%</div>
                        </div>
                    `).join('')}
                </div>
            `;
        }
    }

    calculateWeeklyPerformance() {
        return this.currentTelesales.map(employee => {
            const employeeTasks = this.currentTasks.filter(task => task.assignedTo === employee.id);
            const completed = employeeTasks.filter(task => task.status === 'completed').length;
            const total = employeeTasks.length;
            const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

            return {
                employee: employee.employeeName,
                completed,
                total,
                percentage
            };
        });
    }

    calculateEmployeeRankings() {
        return this.currentTelesales.map(employee => {
            const employeeTasks = this.currentTasks.filter(task => task.assignedTo === employee.id);
            const completedTasks = employeeTasks.filter(task => task.status === 'completed').length;
            const totalTasks = employeeTasks.length;
            const score = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

            return {
                name: employee.employeeName,
                completedTasks,
                totalTasks,
                score
            };
        }).sort((a, b) => b.score - a.score);
    }

    // CRUD operations
    showAddEmployeeModal() {
        console.log('Show add employee modal');
    }

    showAssignTaskModal() {
        console.log('Show assign task modal');
    }

    viewEmployee(employeeId) {
        console.log('View employee:', employeeId);
    }

    editEmployee(employeeId) {
        console.log('Edit employee:', employeeId);
    }

    deleteEmployee(employeeId) {
        if (confirm(getTranslation('confirm_delete'))) {
            const storage = getStorageManager();
            if (storage.deleteTelesales(employeeId)) {
                this.loadData();
                this.renderEmployeesTable();
                this.updateStatistics();

                if (window.exportManager) {
                    exportManager.showNotification(getTranslation('employee_deleted'), 'success');
                }
            }
        }
    }

    assignTaskToEmployee(employeeId) {
        console.log('Assign task to employee:', employeeId);
    }

    viewTask(taskId) {
        console.log('View task:', taskId);
    }

    editTask(taskId) {
        console.log('Edit task:', taskId);
    }

    deleteTask(taskId) {
        if (confirm(getTranslation('confirm_delete'))) {
            const storage = getStorageManager();
            if (storage.deleteTask(taskId)) {
                this.loadData();
                this.renderTasksTable();
                this.updateStatistics();

                if (window.exportManager) {
                    exportManager.showNotification(getTranslation('task_deleted'), 'success');
                }
            }
        }
    }

    exportTelesales() {
        if (window.exportManager) {
            exportManager.showExportModal();
        }
    }

    // Utility methods
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Global telesales manager instance
let telesalesManager;

// Initialize telesales management
function initializeTelesales() {
    telesalesManager = new TelesalesManager();
    return telesalesManager;
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TelesalesManager, initializeTelesales };
}