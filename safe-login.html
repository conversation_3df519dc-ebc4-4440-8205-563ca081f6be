<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول آمن - CRM</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 400px;
        }
        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        .logo i {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        h1 {
            text-align: center;
            color: #333;
            margin: 0 0 2rem 0;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #555;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            box-sizing: border-box;
        }
        input:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            margin-top: 1rem;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .quick-login {
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .quick-btn {
            width: 100%;
            background: #28a745;
            color: white;
            border: none;
            padding: 0.5rem;
            border-radius: 3px;
            margin: 0.25rem 0;
            cursor: pointer;
        }
        .quick-btn:hover {
            background: #218838;
        }
        .quick-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .message {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
            text-align: center;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <i class="fas fa-users-cog"></i>
            <h1>نظام إدارة علاقات العملاء</h1>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" required>
            </div>
            
            <div class="form-group">
                <label for="role">الدور:</label>
                <select id="role" required>
                    <option value="">اختر الدور</option>
                    <option value="manager">مدير</option>
                    <option value="supervisor">مشرف</option>
                    <option value="telesales">مبيعات هاتفية</option>
                </select>
            </div>
            
            <button type="submit" class="btn" id="loginBtn">تسجيل الدخول</button>
        </form>
        
        <div id="message"></div>
        
        <div class="quick-login">
            <p><strong>دخول سريع:</strong></p>
            <button class="quick-btn" onclick="safeQuickLogin('admin', 'admin123', 'manager')">دخول كمدير</button>
            <button class="quick-btn" onclick="safeQuickLogin('supervisor', 'super123', 'supervisor')">دخول كمشرف</button>
            <button class="quick-btn" onclick="safeQuickLogin('sales', 'sales123', 'telesales')">دخول كموظف مبيعات</button>
        </div>
    </div>

    <script>
        // Global error handler
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
            showMessage('حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.', 'error');
        });

        // Simple user database
        const USERS = {
            'admin-admin123-manager': {
                username: 'admin',
                password: 'admin123',
                role: 'manager',
                name: 'مدير النظام'
            },
            'supervisor-super123-supervisor': {
                username: 'supervisor',
                password: 'super123',
                role: 'supervisor',
                name: 'مشرف المبيعات'
            },
            'sales-sales123-telesales': {
                username: 'sales',
                password: 'sales123',
                role: 'telesales',
                name: 'موظف المبيعات'
            }
        };

        let isLoggingIn = false;

        function showMessage(text, type = 'error') {
            try {
                const messageDiv = document.getElementById('message');
                if (messageDiv) {
                    messageDiv.innerHTML = `<div class="message ${type}">${text}</div>`;
                }
            } catch (error) {
                console.error('Error showing message:', error);
                alert(text); // Fallback to alert
            }
        }

        function setButtonsDisabled(disabled) {
            try {
                const loginBtn = document.getElementById('loginBtn');
                const quickBtns = document.querySelectorAll('.quick-btn');
                
                if (loginBtn) loginBtn.disabled = disabled;
                quickBtns.forEach(btn => btn.disabled = disabled);
            } catch (error) {
                console.error('Error setting button states:', error);
            }
        }

        function safeQuickLogin(username, password, role) {
            if (isLoggingIn) return;
            
            try {
                const usernameField = document.getElementById('username');
                const passwordField = document.getElementById('password');
                const roleField = document.getElementById('role');
                
                if (usernameField) usernameField.value = username;
                if (passwordField) passwordField.value = password;
                if (roleField) roleField.value = role;
                
                safeLogin(username, password, role);
            } catch (error) {
                console.error('Quick login error:', error);
                showMessage('خطأ في الدخول السريع. يرجى المحاولة يدوياً.', 'error');
            }
        }

        function safeLogin(username, password, role) {
            if (isLoggingIn) {
                showMessage('جاري تسجيل الدخول...', 'loading');
                return;
            }

            try {
                isLoggingIn = true;
                setButtonsDisabled(true);
                showMessage('<span class="spinner"></span> جاري تسجيل الدخول...', 'loading');

                // Validate inputs
                if (!username || !password || !role) {
                    throw new Error('يرجى ملء جميع الحقول المطلوبة.');
                }

                const key = `${username}-${password}-${role}`;
                const user = USERS[key];
                
                if (user) {
                    // Save user session safely
                    try {
                        localStorage.setItem('currentUser', JSON.stringify(user));
                        localStorage.setItem('isLoggedIn', 'true');
                        localStorage.setItem('loginTime', new Date().toISOString());
                    } catch (storageError) {
                        console.error('Storage error:', storageError);
                        throw new Error('خطأ في حفظ بيانات الجلسة.');
                    }
                    
                    showMessage(`مرحباً ${user.name}! جاري التحويل...`, 'success');
                    
                    // Safe redirect
                    setTimeout(() => {
                        try {
                            // Check if simple-dashboard.html exists, otherwise use index.html
                            window.location.href = 'simple-dashboard.html';
                        } catch (redirectError) {
                            console.error('Redirect error:', redirectError);
                            // Fallback redirect
                            try {
                                window.location.replace('simple-dashboard.html');
                            } catch (fallbackError) {
                                showMessage('تم تسجيل الدخول بنجاح! يرجى الانتقال يدوياً إلى لوحة التحكم.', 'success');
                            }
                        }
                        isLoggingIn = false;
                        setButtonsDisabled(false);
                    }, 2000);
                    
                } else {
                    throw new Error('بيانات الدخول غير صحيحة. يرجى التحقق من اسم المستخدم وكلمة المرور والدور.');
                }
                
            } catch (error) {
                console.error('Login error:', error);
                showMessage(error.message || 'حدث خطأ أثناء تسجيل الدخول.', 'error');
                isLoggingIn = false;
                setButtonsDisabled(false);
            }
        }

        // Safe form submission
        function setupFormHandler() {
            try {
                const form = document.getElementById('loginForm');
                if (form) {
                    form.addEventListener('submit', function(e) {
                        try {
                            e.preventDefault();
                            
                            const username = (document.getElementById('username')?.value || '').trim();
                            const password = (document.getElementById('password')?.value || '').trim();
                            const role = document.getElementById('role')?.value || '';
                            
                            safeLogin(username, password, role);
                        } catch (error) {
                            console.error('Form submission error:', error);
                            showMessage('خطأ في إرسال النموذج. يرجى المحاولة مرة أخرى.', 'error');
                        }
                    });
                }
            } catch (error) {
                console.error('Error setting up form handler:', error);
            }
        }

        // Safe session check
        function checkExistingSession() {
            try {
                if (localStorage.getItem('isLoggedIn') === 'true') {
                    const userStr = localStorage.getItem('currentUser');
                    if (userStr) {
                        const currentUser = JSON.parse(userStr);
                        if (currentUser && currentUser.username) {
                            showMessage(`مرحباً ${currentUser.name}! أنت مسجل دخول بالفعل. جاري التحويل...`, 'success');
                            setTimeout(() => {
                                try {
                                    window.location.href = 'simple-dashboard.html';
                                } catch (error) {
                                    showMessage('يرجى الانتقال يدوياً إلى لوحة التحكم.', 'success');
                                }
                            }, 2000);
                            return;
                        }
                    }
                }
                // Clear invalid session data
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('currentUser');
            } catch (error) {
                console.error('Session check error:', error);
                // Clear potentially corrupted data
                try {
                    localStorage.removeItem('isLoggedIn');
                    localStorage.removeItem('currentUser');
                } catch (clearError) {
                    console.error('Error clearing session:', clearError);
                }
            }
        }

        // Initialize page safely
        function initializePage() {
            try {
                setupFormHandler();
                checkExistingSession();
                console.log('Login page initialized successfully');
            } catch (error) {
                console.error('Initialization error:', error);
                showMessage('خطأ في تحميل الصفحة. يرجى إعادة تحميل الصفحة.', 'error');
            }
        }

        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializePage);
        } else {
            initializePage();
        }
    </script>
</body>
</html>
