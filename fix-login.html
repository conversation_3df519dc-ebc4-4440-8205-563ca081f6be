<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح تسجيل الدخول - CRM Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .fix-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .step {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .fix-btn {
            background: #28a745;
        }
        .fix-btn:hover {
            background: #218838;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="fix-container">
        <h1>🔧 إصلاح مشكلة تسجيل الدخول</h1>
        
        <div class="step">
            <h2>الخطوة 1: مسح البيانات القديمة</h2>
            <p>سيتم مسح جميع البيانات المحفوظة وإعادة إنشائها</p>
            <button onclick="clearOldData()" class="fix-btn">مسح البيانات القديمة</button>
            <div id="clearResult"></div>
        </div>
        
        <div class="step">
            <h2>الخطوة 2: إنشاء بيانات جديدة</h2>
            <p>سيتم إنشاء المستخدمين التجريبيين بالبيانات الصحيحة</p>
            <button onclick="createNewData()" class="fix-btn">إنشاء بيانات جديدة</button>
            <div id="createResult"></div>
        </div>
        
        <div class="step">
            <h2>الخطوة 3: اختبار تسجيل الدخول</h2>
            <p>اختبار تسجيل الدخول مع جميع الحسابات</p>
            <button onclick="testAllLogins()" class="fix-btn">اختبار تسجيل الدخول</button>
            <div id="testResult"></div>
        </div>
        
        <div class="step">
            <h2>الخطوة 4: فحص البيانات المحفوظة</h2>
            <p>فحص البيانات الفعلية المحفوظة في النظام</p>
            <button onclick="debugData()" class="fix-btn">فحص البيانات</button>
            <div id="debugResult"></div>
        </div>

        <div class="step">
            <h2>الخطوة 5: إصلاح يدوي</h2>
            <p>إصلاح المشكلة يدوياً</p>
            <button onclick="manualFix()" class="fix-btn">إصلاح يدوي</button>
            <div id="manualResult"></div>
        </div>

        <div class="step">
            <h2>الخطوة 6: الانتقال إلى النظام</h2>
            <p>بعد نجاح الاختبار، يمكنك الانتقال إلى النظام</p>
            <button onclick="goToLogin()">الانتقال إلى صفحة تسجيل الدخول</button>
            <button onclick="goToDashboard()">الانتقال مباشرة إلى لوحة التحكم</button>
        </div>
    </div>

    <!-- Load CRM Scripts -->
    <script src="js/language.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>

    <script>
        function showResult(containerId, message, type = 'success') {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function clearOldData() {
            try {
                localStorage.clear();
                showResult('clearResult', '✅ تم مسح جميع البيانات القديمة بنجاح', 'success');
            } catch (error) {
                showResult('clearResult', `❌ خطأ في مسح البيانات: ${error.message}`, 'error');
            }
        }

        function createNewData() {
            try {
                // Initialize storage system
                const storage = getStorageManager();
                
                // Create users manually with correct data
                const users = [
                    {
                        id: 1,
                        username: 'admin',
                        password: 'admin123',
                        role: 'manager',
                        name: 'مدير النظام',
                        email: '<EMAIL>',
                        active: true,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 2,
                        username: 'supervisor',
                        password: 'super123',
                        role: 'supervisor',
                        name: 'مشرف المبيعات',
                        email: '<EMAIL>',
                        active: true,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 3,
                        username: 'sales',
                        password: 'sales123',
                        role: 'telesales',
                        name: 'موظف المبيعات',
                        email: '<EMAIL>',
                        active: true,
                        createdAt: new Date().toISOString()
                    }
                ];
                
                // Save users
                storage.saveUsers(users);
                
                // Initialize other data
                storage.initializeDemoData();
                
                const savedUsers = storage.getUsers();
                let results = ['✅ تم إنشاء البيانات الجديدة بنجاح', '<br><strong>المستخدمون المنشأون:</strong>'];
                savedUsers.forEach(user => {
                    results.push(`👤 ${user.username} (${user.role}) - ${user.name}`);
                });
                
                showResult('createResult', results.join('<br>'), 'success');
            } catch (error) {
                showResult('createResult', `❌ خطأ في إنشاء البيانات: ${error.message}`, 'error');
            }
        }

        function debugData() {
            try {
                const storage = getStorageManager();
                const users = storage.getUsers();

                let results = ['<strong>فحص البيانات المحفوظة:</strong>'];

                // Show localStorage content
                results.push('<br><strong>محتوى localStorage:</strong>');
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    const value = localStorage.getItem(key);
                    results.push(`🔑 ${key}: ${value ? value.substring(0, 100) + '...' : 'فارغ'}`);
                }

                // Show users
                results.push('<br><strong>المستخدمون المحفوظون:</strong>');
                if (users.length === 0) {
                    results.push('❌ لا يوجد مستخدمون');
                } else {
                    users.forEach((user, index) => {
                        results.push(`👤 ${index + 1}. اسم المستخدم: "${user.username}" | كلمة المرور: "${user.password}" | الدور: "${user.role}" | نشط: ${user.active}`);
                    });
                }

                // Test getUserByCredentials directly
                results.push('<br><strong>اختبار مباشر للمطابقة:</strong>');
                const testUser = storage.getUserByCredentials('admin', 'admin123', 'manager');
                results.push(`🔍 البحث عن admin/admin123/manager: ${testUser ? 'موجود ✅' : 'غير موجود ❌'}`);

                showResult('debugResult', results.join('<br>'), 'warning');
            } catch (error) {
                showResult('debugResult', `❌ خطأ في فحص البيانات: ${error.message}`, 'error');
            }
        }

        function manualFix() {
            try {
                // Clear everything and start fresh
                localStorage.clear();

                // Manually create the exact data structure
                const userData = [
                    {
                        id: 1,
                        username: 'admin',
                        password: 'admin123',
                        role: 'manager',
                        name: 'مدير النظام',
                        email: '<EMAIL>',
                        active: true,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 2,
                        username: 'supervisor',
                        password: 'super123',
                        role: 'supervisor',
                        name: 'مشرف المبيعات',
                        email: '<EMAIL>',
                        active: true,
                        createdAt: new Date().toISOString()
                    },
                    {
                        id: 3,
                        username: 'sales',
                        password: 'sales123',
                        role: 'telesales',
                        name: 'موظف المبيعات',
                        email: '<EMAIL>',
                        active: true,
                        createdAt: new Date().toISOString()
                    }
                ];

                // Save directly to localStorage
                localStorage.setItem('crm_users', JSON.stringify(userData));

                // Test immediately
                const storage = getStorageManager();
                const savedUsers = storage.getUsers();

                let results = ['✅ تم الإصلاح اليدوي بنجاح'];
                results.push(`📊 عدد المستخدمين المحفوظين: ${savedUsers.length}`);

                // Test each login
                const testAccounts = [
                    { username: 'admin', password: 'admin123', role: 'manager', name: 'مدير' },
                    { username: 'supervisor', password: 'super123', role: 'supervisor', name: 'مشرف' },
                    { username: 'sales', password: 'sales123', role: 'telesales', name: 'مبيعات' }
                ];

                results.push('<br><strong>اختبار فوري:</strong>');
                testAccounts.forEach(account => {
                    const user = storage.getUserByCredentials(account.username, account.password, account.role);
                    if (user) {
                        results.push(`✅ ${account.name}: يعمل بنجاح`);
                    } else {
                        results.push(`❌ ${account.name}: لا يزال لا يعمل`);
                    }
                });

                showResult('manualResult', results.join('<br>'), 'success');
            } catch (error) {
                showResult('manualResult', `❌ خطأ في الإصلاح اليدوي: ${error.message}`, 'error');
            }
        }

        function testAllLogins() {
            try {
                // Initialize auth system
                initializeAuth();

                const testAccounts = [
                    { username: 'admin', password: 'admin123', role: 'manager', name: 'مدير' },
                    { username: 'supervisor', password: 'super123', role: 'supervisor', name: 'مشرف' },
                    { username: 'sales', password: 'sales123', role: 'telesales', name: 'مبيعات' }
                ];

                let results = ['<strong>نتائج اختبار تسجيل الدخول:</strong>'];
                let allSuccess = true;

                testAccounts.forEach(account => {
                    try {
                        const loginResult = login(account.username, account.password, account.role);

                        if (loginResult && loginResult.success) {
                            results.push(`✅ ${account.name}: نجح تسجيل الدخول`);
                            logout(); // Logout after test
                        } else {
                            const message = loginResult ? loginResult.message : 'خطأ غير معروف';
                            results.push(`❌ ${account.name}: فشل - ${message}`);
                            allSuccess = false;
                        }
                    } catch (error) {
                        results.push(`❌ ${account.name}: خطأ - ${error.message}`);
                        allSuccess = false;
                    }
                });

                if (allSuccess) {
                    results.push('<br>🎉 <strong>جميع الحسابات تعمل بنجاح! يمكنك الآن استخدام النظام.</strong>');
                    showResult('testResult', results.join('<br>'), 'success');
                } else {
                    results.push('<br>⚠️ <strong>بعض الحسابات لا تعمل. جرب الإصلاح اليدوي.</strong>');
                    showResult('testResult', results.join('<br>'), 'warning');
                }
            } catch (error) {
                showResult('testResult', `❌ خطأ في اختبار تسجيل الدخول: ${error.message}`, 'error');
            }
        }

        function goToLogin() {
            window.location.href = 'index.html';
        }

        function goToDashboard() {
            // Login as admin first
            try {
                initializeAuth();
                const loginResult = login('admin', 'admin123', 'manager');
                if (loginResult && loginResult.success) {
                    window.location.href = 'dashboard.html';
                } else {
                    alert('فشل في تسجيل الدخول التلقائي. يرجى استخدام صفحة تسجيل الدخول.');
                    goToLogin();
                }
            } catch (error) {
                alert('خطأ في تسجيل الدخول التلقائي: ' + error.message);
                goToLogin();
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            try {
                initializeLanguage();
                console.log('Fix page initialized successfully');
            } catch (error) {
                console.error('Fix page initialization error:', error);
            }
        });
    </script>
</body>
</html>
