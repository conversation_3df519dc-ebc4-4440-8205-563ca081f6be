// Customer Management Module for CRM
class CustomerManager {
    constructor() {
        this.currentCustomers = [];
        this.filteredCustomers = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortField = 'fullName';
        this.sortDirection = 'asc';
        this.filters = {};
        
        this.init();
    }
    
    init() {
        this.loadCustomers();
        this.bindEvents();
    }
    
    bindEvents() {
        // Listen for module navigation
        document.addEventListener('navigateToModule', (e) => {
            if (e.detail.module === 'customers') {
                this.handleNavigation(e.detail);
            }
        });
    }
    
    handleNavigation(detail) {
        if (detail.action === 'view' && detail.id) {
            this.viewCustomer(detail.id);
        } else {
            this.showCustomersModule();
        }
    }
    
    loadCustomers() {
        const storage = getStorageManager();
        this.currentCustomers = storage.getCustomers();
        this.filteredCustomers = [...this.currentCustomers];
        this.applyFiltersAndSort();
    }
    
    showCustomersModule() {
        const moduleContent = this.createCustomersModule();
        const customersModule = document.getElementById('customersModule');
        
        if (customersModule) {
            customersModule.innerHTML = moduleContent;
            this.bindModuleEvents();
            this.renderCustomersTable();
            this.updateStatistics();
        }
    }
    
    createCustomersModule() {
        return `
            <div class="module-header">
                <h2 data-key="customer_management">${getTranslation('customer_management')}</h2>
                <div class="module-actions">
                    <button class="btn btn-primary" id="addCustomerBtn" data-permission="customers:create">
                        <i class="fas fa-plus"></i>
                        <span data-key="add_customer">${getTranslation('add_customer')}</span>
                    </button>
                    <button class="btn btn-secondary" id="exportCustomersBtn" data-permission="customers:export">
                        <i class="fas fa-download"></i>
                        <span data-key="export">${getTranslation('export')}</span>
                    </button>
                </div>
            </div>
            
            <div class="customers-content">
                <!-- Statistics Cards -->
                <div class="stats-row">
                    <div class="stat-card">
                        <div class="stat-icon customers">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalCustomersCount">0</h3>
                            <p data-key="total_customers">${getTranslation('total_customers')}</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon active">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="activeCustomersCount">0</h3>
                            <p data-key="active_customers">${getTranslation('active_customers')}</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon prospects">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="prospectCustomersCount">0</h3>
                            <p data-key="prospect_customers">${getTranslation('prospect_customers')}</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon recent">
                            <i class="fas fa-user-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="recentCustomersCount">0</h3>
                            <p data-key="recent_customers">${getTranslation('recent_customers')}</p>
                        </div>
                    </div>
                </div>
                
                <!-- Filters and Search -->
                <div class="filters-section">
                    <div class="filters-row">
                        <div class="filter-group">
                            <label data-key="search">${getTranslation('search')}</label>
                            <div class="search-input">
                                <i class="fas fa-search"></i>
                                <input type="text" id="customerSearch" placeholder="${getTranslation('search_customers')}" data-key-placeholder="search_customers">
                            </div>
                        </div>
                        
                        <div class="filter-group">
                            <label data-key="status">${getTranslation('status')}</label>
                            <select id="statusFilter">
                                <option value="">${getTranslation('all_statuses')}</option>
                                <option value="active">${getTranslation('active')}</option>
                                <option value="inactive">${getTranslation('inactive')}</option>
                                <option value="prospect">${getTranslation('prospect')}</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label data-key="city">${getTranslation('city')}</label>
                            <select id="cityFilter">
                                <option value="">${getTranslation('all_cities')}</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label data-key="business_type">${getTranslation('business_type')}</label>
                            <select id="businessTypeFilter">
                                <option value="">${getTranslation('all_types')}</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <button class="btn btn-secondary" id="clearFiltersBtn">
                                <i class="fas fa-times"></i>
                                <span data-key="clear_filters">${getTranslation('clear_filters')}</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Customers Table -->
                <div class="table-container">
                    <div class="table-header">
                        <div class="table-info">
                            <span id="tableInfo">${getTranslation('showing')} 0 ${getTranslation('of')} 0 ${getTranslation('entries')}</span>
                        </div>
                        <div class="table-controls">
                            <div class="page-size-selector">
                                <label data-key="show">${getTranslation('show')}</label>
                                <select id="pageSizeSelect">
                                    <option value="10">10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                                <span data-key="entries">${getTranslation('entries')}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-wrapper">
                        <table class="data-table" id="customersTable">
                            <thead>
                                <tr>
                                    <th data-sort="fullName" class="sortable">
                                        <span data-key="full_name">${getTranslation('full_name')}</span>
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th data-sort="mobilePhone" class="sortable">
                                        <span data-key="mobile_phone">${getTranslation('mobile_phone')}</span>
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th data-sort="email" class="sortable">
                                        <span data-key="email">${getTranslation('email')}</span>
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th data-sort="city" class="sortable">
                                        <span data-key="city">${getTranslation('city')}</span>
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th data-sort="businessType" class="sortable">
                                        <span data-key="business_type">${getTranslation('business_type')}</span>
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th data-sort="status" class="sortable">
                                        <span data-key="status">${getTranslation('status')}</span>
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th data-sort="lastContactDate" class="sortable">
                                        <span data-key="last_contact">${getTranslation('last_contact')}</span>
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="actions-column">
                                        <span data-key="actions">${getTranslation('actions')}</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="customersTableBody">
                                <!-- Table rows will be inserted here -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="table-footer">
                        <div class="pagination" id="customersPagination">
                            <!-- Pagination will be inserted here -->
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    bindModuleEvents() {
        // Add customer button
        const addBtn = document.getElementById('addCustomerBtn');
        if (addBtn && hasPermission('customers', 'create')) {
            addBtn.addEventListener('click', () => this.showAddCustomerModal());
        }
        
        // Export button
        const exportBtn = document.getElementById('exportCustomersBtn');
        if (exportBtn && hasPermission('customers', 'export')) {
            exportBtn.addEventListener('click', () => this.exportCustomers());
        }
        
        // Search input
        const searchInput = document.getElementById('customerSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filters.search = e.target.value;
                this.applyFiltersAndSort();
                this.renderCustomersTable();
            });
        }
        
        // Filter selects
        ['statusFilter', 'cityFilter', 'businessTypeFilter'].forEach(filterId => {
            const filterElement = document.getElementById(filterId);
            if (filterElement) {
                filterElement.addEventListener('change', (e) => {
                    const filterType = filterId.replace('Filter', '');
                    this.filters[filterType] = e.target.value;
                    this.applyFiltersAndSort();
                    this.renderCustomersTable();
                });
            }
        });
        
        // Clear filters button
        const clearFiltersBtn = document.getElementById('clearFiltersBtn');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => this.clearFilters());
        }
        
        // Page size selector
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        if (pageSizeSelect) {
            pageSizeSelect.value = this.pageSize;
            pageSizeSelect.addEventListener('change', (e) => {
                this.pageSize = parseInt(e.target.value);
                this.currentPage = 1;
                this.renderCustomersTable();
            });
        }
        
        // Table sorting
        const sortableHeaders = document.querySelectorAll('#customersTable th.sortable');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', () => {
                const field = header.getAttribute('data-sort');
                this.handleSort(field);
            });
        });
    }
    
    applyFiltersAndSort() {
        let filtered = [...this.currentCustomers];
        
        // Apply search filter
        if (this.filters.search) {
            const searchTerm = this.filters.search.toLowerCase();
            filtered = filtered.filter(customer => 
                Object.values(customer).some(value => 
                    value && value.toString().toLowerCase().includes(searchTerm)
                )
            );
        }
        
        // Apply status filter
        if (this.filters.status) {
            filtered = filtered.filter(customer => customer.status === this.filters.status);
        }
        
        // Apply city filter
        if (this.filters.city) {
            filtered = filtered.filter(customer => customer.city === this.filters.city);
        }
        
        // Apply business type filter
        if (this.filters.businessType) {
            filtered = filtered.filter(customer => customer.businessType === this.filters.businessType);
        }
        
        // Apply sorting
        filtered.sort((a, b) => {
            const aValue = a[this.sortField] || '';
            const bValue = b[this.sortField] || '';
            
            if (this.sortDirection === 'asc') {
                return aValue.toString().localeCompare(bValue.toString());
            } else {
                return bValue.toString().localeCompare(aValue.toString());
            }
        });
        
        this.filteredCustomers = filtered;
        this.currentPage = 1; // Reset to first page when filters change
    }
    
    handleSort(field) {
        if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortDirection = 'asc';
        }
        
        this.applyFiltersAndSort();
        this.renderCustomersTable();
        this.updateSortIcons();
    }
    
    updateSortIcons() {
        // Reset all sort icons
        document.querySelectorAll('#customersTable th.sortable i').forEach(icon => {
            icon.className = 'fas fa-sort';
        });
        
        // Set active sort icon
        const activeHeader = document.querySelector(`#customersTable th[data-sort="${this.sortField}"] i`);
        if (activeHeader) {
            activeHeader.className = `fas fa-sort-${this.sortDirection === 'asc' ? 'up' : 'down'}`;
        }
    }
    
    renderCustomersTable() {
        const tbody = document.getElementById('customersTableBody');
        if (!tbody) return;
        
        // Calculate pagination
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageCustomers = this.filteredCustomers.slice(startIndex, endIndex);
        
        // Render table rows
        tbody.innerHTML = pageCustomers.map(customer => this.createCustomerRow(customer)).join('');
        
        // Update table info
        this.updateTableInfo();
        
        // Render pagination
        this.renderPagination();
        
        // Bind row events
        this.bindRowEvents();
        
        // Update filter options
        this.updateFilterOptions();
    }
    
    createCustomerRow(customer) {
        const statusClass = customer.status || 'inactive';
        const statusText = getTranslation(customer.status) || customer.status;
        const lastContact = customer.lastContactDate ? 
            new Date(customer.lastContactDate).toLocaleDateString() : '-';
        
        return `
            <tr data-customer-id="${customer.id}">
                <td>
                    <div class="customer-info">
                        <div class="customer-name">${this.escapeHtml(customer.fullName || '')}</div>
                        ${customer.notes ? `<div class="customer-notes">${this.escapeHtml(customer.notes.substring(0, 50))}${customer.notes.length > 50 ? '...' : ''}</div>` : ''}
                    </div>
                </td>
                <td>
                    <a href="tel:${customer.mobilePhone || ''}" class="phone-link">
                        ${this.escapeHtml(customer.mobilePhone || '')}
                    </a>
                </td>
                <td>
                    <a href="mailto:${customer.email || ''}" class="email-link">
                        ${this.escapeHtml(customer.email || '')}
                    </a>
                </td>
                <td>${this.escapeHtml(customer.city || '')}</td>
                <td>${this.escapeHtml(customer.businessType || '')}</td>
                <td>
                    <span class="status-badge status-${statusClass}">${statusText}</span>
                </td>
                <td>${lastContact}</td>
                <td class="actions-cell">
                    <div class="action-buttons">
                        <button class="btn-action btn-view" onclick="customerManager.viewCustomer('${customer.id}')" title="${getTranslation('view')}">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${hasPermission('customers', 'update') ? `
                            <button class="btn-action btn-edit" onclick="customerManager.editCustomer('${customer.id}')" title="${getTranslation('edit')}">
                                <i class="fas fa-edit"></i>
                            </button>
                        ` : ''}
                        ${hasPermission('customers', 'delete') ? `
                            <button class="btn-action btn-delete" onclick="customerManager.deleteCustomer('${customer.id}')" title="${getTranslation('delete')}">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    }
    
    bindRowEvents() {
        // Double-click to view customer
        document.querySelectorAll('#customersTableBody tr').forEach(row => {
            row.addEventListener('dblclick', () => {
                const customerId = row.getAttribute('data-customer-id');
                this.viewCustomer(customerId);
            });
        });
    }
    
    updateTableInfo() {
        const tableInfo = document.getElementById('tableInfo');
        if (tableInfo) {
            const startIndex = (this.currentPage - 1) * this.pageSize + 1;
            const endIndex = Math.min(this.currentPage * this.pageSize, this.filteredCustomers.length);
            const total = this.filteredCustomers.length;
            
            tableInfo.textContent = `${getTranslation('showing')} ${startIndex}-${endIndex} ${getTranslation('of')} ${total} ${getTranslation('entries')}`;
        }
    }
    
    renderPagination() {
        const pagination = document.getElementById('customersPagination');
        if (!pagination) return;
        
        const totalPages = Math.ceil(this.filteredCustomers.length / this.pageSize);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        let html = '';
        
        // Previous button
        html += `
            <button class="pagination-btn ${this.currentPage === 1 ? 'disabled' : ''}" 
                    onclick="customerManager.goToPage(${this.currentPage - 1})" 
                    ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
                <span data-key="previous">${getTranslation('previous')}</span>
            </button>
        `;
        
        // Page numbers
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);
        
        if (startPage > 1) {
            html += `<button class="pagination-btn" onclick="customerManager.goToPage(1)">1</button>`;
            if (startPage > 2) {
                html += `<span class="pagination-ellipsis">...</span>`;
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                        onclick="customerManager.goToPage(${i})">
                    ${i}
                </button>
            `;
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += `<span class="pagination-ellipsis">...</span>`;
            }
            html += `<button class="pagination-btn" onclick="customerManager.goToPage(${totalPages})">${totalPages}</button>`;
        }
        
        // Next button
        html += `
            <button class="pagination-btn ${this.currentPage === totalPages ? 'disabled' : ''}" 
                    onclick="customerManager.goToPage(${this.currentPage + 1})" 
                    ${this.currentPage === totalPages ? 'disabled' : ''}>
                <span data-key="next">${getTranslation('next')}</span>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        pagination.innerHTML = html;
    }
    
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredCustomers.length / this.pageSize);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderCustomersTable();
        }
    }
    
    updateStatistics() {
        const stats = {
            total: this.currentCustomers.length,
            active: this.currentCustomers.filter(c => c.status === 'active').length,
            prospects: this.currentCustomers.filter(c => c.status === 'prospect').length,
            recent: this.currentCustomers.filter(c => {
                const createdDate = new Date(c.createdAt);
                const weekAgo = new Date();
                weekAgo.setDate(weekAgo.getDate() - 7);
                return createdDate > weekAgo;
            }).length
        };
        
        document.getElementById('totalCustomersCount').textContent = stats.total;
        document.getElementById('activeCustomersCount').textContent = stats.active;
        document.getElementById('prospectCustomersCount').textContent = stats.prospects;
        document.getElementById('recentCustomersCount').textContent = stats.recent;
    }
    
    updateFilterOptions() {
        // Update city filter options
        const cities = [...new Set(this.currentCustomers.map(c => c.city).filter(Boolean))].sort();
        const cityFilter = document.getElementById('cityFilter');
        if (cityFilter) {
            const currentValue = cityFilter.value;
            cityFilter.innerHTML = `<option value="">${getTranslation('all_cities')}</option>` +
                cities.map(city => `<option value="${city}">${city}</option>`).join('');
            cityFilter.value = currentValue;
        }
        
        // Update business type filter options
        const businessTypes = [...new Set(this.currentCustomers.map(c => c.businessType).filter(Boolean))].sort();
        const businessTypeFilter = document.getElementById('businessTypeFilter');
        if (businessTypeFilter) {
            const currentValue = businessTypeFilter.value;
            businessTypeFilter.innerHTML = `<option value="">${getTranslation('all_types')}</option>` +
                businessTypes.map(type => `<option value="${type}">${type}</option>`).join('');
            businessTypeFilter.value = currentValue;
        }
    }
    
    clearFilters() {
        this.filters = {};
        this.currentPage = 1;
        
        // Reset filter inputs
        document.getElementById('customerSearch').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('cityFilter').value = '';
        document.getElementById('businessTypeFilter').value = '';
        
        this.applyFiltersAndSort();
        this.renderCustomersTable();
    }
    
    // Customer CRUD operations
    showAddCustomerModal() {
        // Implementation for add customer modal
        console.log('Show add customer modal');
    }
    
    viewCustomer(customerId) {
        // Implementation for view customer
        console.log('View customer:', customerId);
    }
    
    editCustomer(customerId) {
        // Implementation for edit customer
        console.log('Edit customer:', customerId);
    }
    
    deleteCustomer(customerId) {
        if (confirm(getTranslation('confirm_delete'))) {
            const storage = getStorageManager();
            if (storage.deleteCustomer(customerId)) {
                this.loadCustomers();
                this.renderCustomersTable();
                this.updateStatistics();
                
                // Show success message
                if (window.exportManager) {
                    exportManager.showNotification(getTranslation('customer_deleted'), 'success');
                }
            }
        }
    }
    
    exportCustomers() {
        if (window.exportManager) {
            exportManager.exportCustomersToExcel();
        }
    }
    
    // Utility methods
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Global customer manager instance
let customerManager;

// Initialize customer management
function initializeCustomers() {
    customerManager = new CustomerManager();
    return customerManager;
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CustomerManager, initializeCustomers };
}
