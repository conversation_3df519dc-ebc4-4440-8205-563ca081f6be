// Data Export System for CRM (Excel & PDF)
class ExportManager {
    constructor() {
        this.init();
    }
    
    init() {
        this.bindEvents();
    }
    
    bindEvents() {
        // Export button click handler
        document.addEventListener('click', (e) => {
            if (e.target.closest('#exportData')) {
                this.showExportModal();
            }
        });
    }
    
    // Show export options modal
    showExportModal() {
        const modal = this.createExportModal();
        document.body.appendChild(modal);
        
        // Show modal
        setTimeout(() => {
            modal.classList.add('active');
        }, 10);
        
        // Bind modal events
        this.bindModalEvents(modal);
    }
    
    createExportModal() {
        const modal = document.createElement('div');
        modal.className = 'export-modal';
        modal.innerHTML = `
            <div class="modal-backdrop"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3 data-key="export_data">${getTranslation('export_data')}</h3>
                    <button class="modal-close" type="button">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="export-options">
                        <div class="export-section">
                            <h4 data-key="select_data_type">${getTranslation('select_data_type')}</h4>
                            <div class="export-checkboxes">
                                <label class="export-checkbox">
                                    <input type="checkbox" name="exportType" value="customers" checked>
                                    <span class="checkmark"></span>
                                    <span data-key="customers">${getTranslation('customers')}</span>
                                    <span class="count" id="customersCount">(0)</span>
                                </label>
                                <label class="export-checkbox">
                                    <input type="checkbox" name="exportType" value="suppliers" checked>
                                    <span class="checkmark"></span>
                                    <span data-key="suppliers">${getTranslation('suppliers')}</span>
                                    <span class="count" id="suppliersCount">(0)</span>
                                </label>
                                <label class="export-checkbox">
                                    <input type="checkbox" name="exportType" value="telesales" checked>
                                    <span class="checkmark"></span>
                                    <span data-key="telesales_team">${getTranslation('telesales_team')}</span>
                                    <span class="count" id="telesalesCount">(0)</span>
                                </label>
                                <label class="export-checkbox">
                                    <input type="checkbox" name="exportType" value="tasks" checked>
                                    <span class="checkmark"></span>
                                    <span data-key="tasks">${getTranslation('tasks')}</span>
                                    <span class="count" id="tasksCount">(0)</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="export-section">
                            <h4 data-key="select_format">${getTranslation('select_format')}</h4>
                            <div class="export-formats">
                                <label class="export-format">
                                    <input type="radio" name="exportFormat" value="excel" checked>
                                    <div class="format-option">
                                        <i class="fas fa-file-excel"></i>
                                        <span data-key="excel_format">Excel (.xlsx)</span>
                                    </div>
                                </label>
                                <label class="export-format">
                                    <input type="radio" name="exportFormat" value="pdf">
                                    <div class="format-option">
                                        <i class="fas fa-file-pdf"></i>
                                        <span data-key="pdf_format">PDF (.pdf)</span>
                                    </div>
                                </label>
                            </div>
                        </div>
                        
                        <div class="export-section">
                            <h4 data-key="export_options">${getTranslation('export_options')}</h4>
                            <div class="export-settings">
                                <label class="export-setting">
                                    <input type="checkbox" id="includeHeaders" checked>
                                    <span data-key="include_headers">${getTranslation('include_headers')}</span>
                                </label>
                                <label class="export-setting">
                                    <input type="checkbox" id="includeTimestamp" checked>
                                    <span data-key="include_timestamp">${getTranslation('include_timestamp')}</span>
                                </label>
                                <label class="export-setting">
                                    <input type="checkbox" id="includeStats">
                                    <span data-key="include_statistics">${getTranslation('include_statistics')}</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-action="cancel">
                        <span data-key="cancel">${getTranslation('cancel')}</span>
                    </button>
                    <button type="button" class="btn btn-primary" data-action="export">
                        <i class="fas fa-download"></i>
                        <span data-key="export">${getTranslation('export')}</span>
                    </button>
                </div>
            </div>
        `;
        
        // Update counts
        this.updateExportCounts(modal);
        
        return modal;
    }
    
    updateExportCounts(modal) {
        const storage = getStorageManager();
        const stats = storage.getStatistics();
        
        modal.querySelector('#customersCount').textContent = `(${stats.customers.total})`;
        modal.querySelector('#suppliersCount').textContent = `(${stats.suppliers.total})`;
        modal.querySelector('#telesalesCount').textContent = `(${stats.telesales.total})`;
        modal.querySelector('#tasksCount').textContent = `(${stats.tasks.total})`;
    }
    
    bindModalEvents(modal) {
        // Close modal events
        modal.querySelector('.modal-close').addEventListener('click', () => {
            this.closeModal(modal);
        });
        
        modal.querySelector('.modal-backdrop').addEventListener('click', () => {
            this.closeModal(modal);
        });
        
        modal.querySelector('[data-action="cancel"]').addEventListener('click', () => {
            this.closeModal(modal);
        });
        
        // Export button
        modal.querySelector('[data-action="export"]').addEventListener('click', () => {
            this.performExport(modal);
        });
        
        // Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.classList.contains('active')) {
                this.closeModal(modal);
            }
        });
    }
    
    closeModal(modal) {
        modal.classList.remove('active');
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 300);
    }
    
    async performExport(modal) {
        try {
            // Get selected options
            const selectedTypes = Array.from(modal.querySelectorAll('input[name="exportType"]:checked'))
                .map(input => input.value);
            
            const format = modal.querySelector('input[name="exportFormat"]:checked').value;
            const includeHeaders = modal.querySelector('#includeHeaders').checked;
            const includeTimestamp = modal.querySelector('#includeTimestamp').checked;
            const includeStats = modal.querySelector('#includeStats').checked;
            
            if (selectedTypes.length === 0) {
                this.showNotification(getTranslation('select_data_to_export'), 'warning');
                return;
            }
            
            // Show loading
            const exportBtn = modal.querySelector('[data-action="export"]');
            const originalText = exportBtn.innerHTML;
            exportBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${getTranslation('exporting')}`;
            exportBtn.disabled = true;
            
            // Prepare data
            const exportData = this.prepareExportData(selectedTypes, {
                includeHeaders,
                includeTimestamp,
                includeStats
            });
            
            // Perform export based on format
            if (format === 'excel') {
                await this.exportToExcel(exportData, selectedTypes);
            } else if (format === 'pdf') {
                await this.exportToPDF(exportData, selectedTypes);
            }
            
            // Restore button
            exportBtn.innerHTML = originalText;
            exportBtn.disabled = false;
            
            // Close modal
            this.closeModal(modal);
            
            // Show success message
            this.showNotification(getTranslation('data_exported'), 'success');
            
        } catch (error) {
            console.error('Export error:', error);
            this.showNotification(getTranslation('export_error'), 'error');
            
            // Restore button
            const exportBtn = modal.querySelector('[data-action="export"]');
            exportBtn.innerHTML = originalText;
            exportBtn.disabled = false;
        }
    }
    
    prepareExportData(selectedTypes, options) {
        const storage = getStorageManager();
        const data = {};
        
        selectedTypes.forEach(type => {
            switch (type) {
                case 'customers':
                    data.customers = storage.getCustomers();
                    break;
                case 'suppliers':
                    data.suppliers = storage.getSuppliers();
                    break;
                case 'telesales':
                    data.telesales = storage.getTelesales();
                    break;
                case 'tasks':
                    data.tasks = storage.getTasks();
                    break;
            }
        });
        
        // Add metadata
        data.metadata = {
            exportDate: new Date().toISOString(),
            exportedBy: getCurrentUser()?.name || 'Unknown',
            totalRecords: Object.values(data).reduce((sum, records) => {
                return sum + (Array.isArray(records) ? records.length : 0);
            }, 0),
            options: options
        };
        
        if (options.includeStats) {
            data.statistics = storage.getStatistics();
        }
        
        return data;
    }
    
    async exportToExcel(data, selectedTypes) {
        try {
            // Create workbook
            const wb = XLSX.utils.book_new();
            
            // Add worksheets for each data type
            selectedTypes.forEach(type => {
                if (data[type] && data[type].length > 0) {
                    const ws = XLSX.utils.json_to_sheet(data[type]);
                    XLSX.utils.book_append_sheet(wb, ws, this.getSheetName(type));
                }
            });
            
            // Add metadata sheet
            if (data.metadata) {
                const metadataArray = Object.entries(data.metadata).map(([key, value]) => ({
                    Property: key,
                    Value: typeof value === 'object' ? JSON.stringify(value) : value
                }));
                const metadataWs = XLSX.utils.json_to_sheet(metadataArray);
                XLSX.utils.book_append_sheet(wb, metadataWs, 'Metadata');
            }
            
            // Add statistics sheet
            if (data.statistics) {
                const statsArray = this.flattenStatistics(data.statistics);
                const statsWs = XLSX.utils.json_to_sheet(statsArray);
                XLSX.utils.book_append_sheet(wb, statsWs, 'Statistics');
            }
            
            // Generate filename
            const filename = this.generateFilename('excel', selectedTypes);
            
            // Save file
            XLSX.writeFile(wb, filename);
            
        } catch (error) {
            console.error('Excel export error:', error);
            throw new Error('Failed to export to Excel');
        }
    }
    
    async exportToPDF(data, selectedTypes) {
        try {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();
            
            // Set font for Arabic support (if available)
            if (getCurrentLanguage() === 'ar') {
                // Note: For proper Arabic support, you'd need to load Arabic fonts
                doc.setLanguage('ar');
            }
            
            let yPosition = 20;
            
            // Add title
            doc.setFontSize(16);
            doc.text(getTranslation('crm_data_export'), 20, yPosition);
            yPosition += 20;
            
            // Add export info
            doc.setFontSize(10);
            doc.text(`${getTranslation('export_date')}: ${new Date().toLocaleString()}`, 20, yPosition);
            yPosition += 10;
            doc.text(`${getTranslation('exported_by')}: ${getCurrentUser()?.name || 'Unknown'}`, 20, yPosition);
            yPosition += 20;
            
            // Add data for each type
            selectedTypes.forEach(type => {
                if (data[type] && data[type].length > 0) {
                    yPosition = this.addDataToPDF(doc, type, data[type], yPosition);
                }
            });
            
            // Generate filename
            const filename = this.generateFilename('pdf', selectedTypes);
            
            // Save PDF
            doc.save(filename);
            
        } catch (error) {
            console.error('PDF export error:', error);
            throw new Error('Failed to export to PDF');
        }
    }
    
    addDataToPDF(doc, type, records, startY) {
        let yPosition = startY;
        
        // Add section title
        doc.setFontSize(14);
        doc.text(getTranslation(type), 20, yPosition);
        yPosition += 15;
        
        // Add table headers and data
        doc.setFontSize(8);
        
        if (records.length > 0) {
            const headers = Object.keys(records[0]).filter(key => 
                !['id', 'createdAt', 'updatedAt'].includes(key)
            );
            
            // Add headers
            let xPosition = 20;
            headers.forEach(header => {
                doc.text(header, xPosition, yPosition);
                xPosition += 40;
            });
            yPosition += 10;
            
            // Add data rows (limit to prevent overflow)
            const maxRows = Math.min(records.length, 20);
            for (let i = 0; i < maxRows; i++) {
                const record = records[i];
                xPosition = 20;
                
                headers.forEach(header => {
                    const value = record[header] || '';
                    const displayValue = typeof value === 'string' ? 
                        value.substring(0, 15) : value.toString().substring(0, 15);
                    doc.text(displayValue, xPosition, yPosition);
                    xPosition += 40;
                });
                
                yPosition += 8;
                
                // Check if we need a new page
                if (yPosition > 270) {
                    doc.addPage();
                    yPosition = 20;
                }
            }
            
            if (records.length > maxRows) {
                doc.text(`... and ${records.length - maxRows} more records`, 20, yPosition);
                yPosition += 10;
            }
        } else {
            doc.text(getTranslation('no_data'), 20, yPosition);
            yPosition += 10;
        }
        
        return yPosition + 20;
    }
    
    getSheetName(type) {
        const names = {
            customers: 'Customers',
            suppliers: 'Suppliers',
            telesales: 'Telesales',
            tasks: 'Tasks'
        };
        return names[type] || type;
    }
    
    flattenStatistics(stats) {
        const flattened = [];
        
        Object.entries(stats).forEach(([category, values]) => {
            if (typeof values === 'object') {
                Object.entries(values).forEach(([key, value]) => {
                    flattened.push({
                        Category: category,
                        Metric: key,
                        Value: value
                    });
                });
            } else {
                flattened.push({
                    Category: category,
                    Metric: 'total',
                    Value: values
                });
            }
        });
        
        return flattened;
    }
    
    generateFilename(format, selectedTypes) {
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const typesStr = selectedTypes.join('-');
        const extension = format === 'excel' ? 'xlsx' : 'pdf';
        
        return `CRM-Export-${typesStr}-${timestamp}.${extension}`;
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Add to container
        let container = document.getElementById('notificationContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notificationContainer';
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
        
        container.appendChild(notification);
        
        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Auto hide after 5 seconds
        setTimeout(() => {
            this.hideNotification(notification);
        }, 5000);
        
        // Close button
        notification.querySelector('.notification-close').addEventListener('click', () => {
            this.hideNotification(notification);
        });
    }
    
    hideNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }
    
    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
    
    // Quick export methods
    exportCustomersToExcel() {
        const storage = getStorageManager();
        const customers = storage.getCustomers();
        
        if (customers.length === 0) {
            this.showNotification(getTranslation('no_customers_to_export'), 'warning');
            return;
        }
        
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(customers);
        XLSX.utils.book_append_sheet(wb, ws, 'Customers');
        
        const filename = `Customers-${new Date().toISOString().slice(0, 10)}.xlsx`;
        XLSX.writeFile(wb, filename);
        
        this.showNotification(getTranslation('customers_exported'), 'success');
    }
    
    exportSuppliersToExcel() {
        const storage = getStorageManager();
        const suppliers = storage.getSuppliers();
        
        if (suppliers.length === 0) {
            this.showNotification(getTranslation('no_suppliers_to_export'), 'warning');
            return;
        }
        
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(suppliers);
        XLSX.utils.book_append_sheet(wb, ws, 'Suppliers');
        
        const filename = `Suppliers-${new Date().toISOString().slice(0, 10)}.xlsx`;
        XLSX.writeFile(wb, filename);
        
        this.showNotification(getTranslation('suppliers_exported'), 'success');
    }
}

// Global export manager instance
let exportManager;

// Initialize export system
function initializeExport() {
    exportManager = new ExportManager();
    return exportManager;
}

// Helper functions for global access
function exportData(type, format) {
    if (exportManager) {
        if (type === 'customers' && format === 'excel') {
            exportManager.exportCustomersToExcel();
        } else if (type === 'suppliers' && format === 'excel') {
            exportManager.exportSuppliersToExcel();
        } else {
            exportManager.showExportModal();
        }
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ExportManager, initializeExport, exportData };
}
