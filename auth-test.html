<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-btn {
            background: #28a745;
        }
        .test-btn:hover {
            background: #218838;
        }
        .clear-btn {
            background: #dc3545;
        }
        .clear-btn:hover {
            background: #c82333;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            border: 1px solid #e9ecef;
        }
        .credential-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .credential-table th,
        .credential-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .credential-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 5px;
        }
        .status-success {
            background: #28a745;
        }
        .status-error {
            background: #dc3545;
        }
        .status-pending {
            background: #ffc107;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔐 Authentication System Test Suite</h1>
        <p>This page tests all authentication functions to ensure they work correctly.</p>
        
        <div class="test-section">
            <h2>1. User Database Verification</h2>
            <button class="test-btn" onclick="testUserDatabase()">Test User Database</button>
            <div id="userDatabaseResult"></div>
        </div>
        
        <div class="test-section">
            <h2>2. Authentication Function Test</h2>
            <button class="test-btn" onclick="testAuthenticationFunction()">Test Authentication Logic</button>
            <div id="authFunctionResult"></div>
        </div>
        
        <div class="test-section">
            <h2>3. All User Accounts Test</h2>
            <button class="test-btn" onclick="testAllUserAccounts()">Test All User Accounts</button>
            <div id="allAccountsResult"></div>
        </div>
        
        <div class="test-section">
            <h2>4. Session Management Test</h2>
            <button class="test-btn" onclick="testSessionManagement()">Test Session Management</button>
            <div id="sessionResult"></div>
        </div>
        
        <div class="test-section">
            <h2>5. Invalid Credentials Test</h2>
            <button class="test-btn" onclick="testInvalidCredentials()">Test Invalid Credentials</button>
            <div id="invalidCredsResult"></div>
        </div>
        
        <div class="test-section">
            <h2>6. Live Login Test</h2>
            <p>Test actual login functionality:</p>
            <button onclick="window.open('bulletproof-login.html', '_blank')">Open Login Page</button>
            <button onclick="window.open('simple-dashboard.html', '_blank')">Open Dashboard</button>
        </div>
        
        <div class="test-section">
            <h2>7. System Cleanup</h2>
            <button class="clear-btn" onclick="clearAllData()">Clear All Session Data</button>
            <button class="clear-btn" onclick="resetTests()">Reset All Tests</button>
            <div id="cleanupResult"></div>
        </div>
    </div>

    <script>
        // Copy the authentication logic from bulletproof-login.html
        const VALID_USERS = [
            {
                username: 'admin',
                password: 'admin123',
                role: 'manager',
                name: 'مدير النظام',
                permissions: ['all']
            },
            {
                username: 'supervisor',
                password: 'super123',
                role: 'supervisor',
                name: 'مشرف المبيعات',
                permissions: ['read', 'write', 'manage_team']
            },
            {
                username: 'sales',
                password: 'sales123',
                role: 'telesales',
                name: 'موظف المبيعات',
                permissions: ['read', 'write_own']
            }
        ];

        function authenticateUser(username, password, role) {
            if (!username || !password || !role) {
                return {
                    success: false,
                    message: 'جميع الحقول مطلوبة',
                    user: null
                };
            }

            username = username.trim().toLowerCase();
            password = password.trim();
            role = role.trim().toLowerCase();

            const user = VALID_USERS.find(u => 
                u.username.toLowerCase() === username &&
                u.password === password &&
                u.role.toLowerCase() === role
            );

            if (user) {
                return {
                    success: true,
                    message: `مرحباً ${user.name}!`,
                    user: user
                };
            } else {
                return {
                    success: false,
                    message: 'بيانات الدخول غير صحيحة',
                    user: null
                };
            }
        }

        function showResult(containerId, content, type = 'info') {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="test-result ${type}">${content}</div>`;
        }

        function testUserDatabase() {
            let results = ['<h3>User Database Test Results:</h3>'];
            
            results.push('<table class="credential-table">');
            results.push('<tr><th>Username</th><th>Password</th><th>Role</th><th>Name</th><th>Status</th></tr>');
            
            VALID_USERS.forEach(user => {
                const status = user.username && user.password && user.role && user.name ? 
                    '<span class="status-indicator status-success"></span>Valid' : 
                    '<span class="status-indicator status-error"></span>Invalid';
                
                results.push(`<tr>
                    <td>${user.username}</td>
                    <td>${user.password}</td>
                    <td>${user.role}</td>
                    <td>${user.name}</td>
                    <td>${status}</td>
                </tr>`);
            });
            
            results.push('</table>');
            results.push(`<p><strong>Total Users:</strong> ${VALID_USERS.length}</p>`);
            
            showResult('userDatabaseResult', results.join(''), 'success');
        }

        function testAuthenticationFunction() {
            let results = ['<h3>Authentication Function Test Results:</h3>'];
            
            // Test valid credentials
            const validTest = authenticateUser('admin', 'admin123', 'manager');
            results.push(`<p><strong>Valid Credentials Test:</strong> ${validTest.success ? '✅ PASS' : '❌ FAIL'}</p>`);
            results.push(`<p>Message: ${validTest.message}</p>`);
            
            // Test invalid credentials
            const invalidTest = authenticateUser('wrong', 'wrong', 'wrong');
            results.push(`<p><strong>Invalid Credentials Test:</strong> ${!invalidTest.success ? '✅ PASS' : '❌ FAIL'}</p>`);
            results.push(`<p>Message: ${invalidTest.message}</p>`);
            
            // Test empty credentials
            const emptyTest = authenticateUser('', '', '');
            results.push(`<p><strong>Empty Credentials Test:</strong> ${!emptyTest.success ? '✅ PASS' : '❌ FAIL'}</p>`);
            results.push(`<p>Message: ${emptyTest.message}</p>`);
            
            showResult('authFunctionResult', results.join(''), 'info');
        }

        function testAllUserAccounts() {
            let results = ['<h3>All User Accounts Test Results:</h3>'];
            let allPassed = true;
            
            results.push('<table class="credential-table">');
            results.push('<tr><th>Account</th><th>Username</th><th>Password</th><th>Role</th><th>Test Result</th></tr>');
            
            const testCases = [
                { name: 'Manager', username: 'admin', password: 'admin123', role: 'manager' },
                { name: 'Supervisor', username: 'supervisor', password: 'super123', role: 'supervisor' },
                { name: 'Telesales', username: 'sales', password: 'sales123', role: 'telesales' }
            ];
            
            testCases.forEach(testCase => {
                const result = authenticateUser(testCase.username, testCase.password, testCase.role);
                const status = result.success ? 
                    '<span class="status-indicator status-success"></span>✅ PASS' : 
                    '<span class="status-indicator status-error"></span>❌ FAIL';
                
                if (!result.success) allPassed = false;
                
                results.push(`<tr>
                    <td>${testCase.name}</td>
                    <td>${testCase.username}</td>
                    <td>${testCase.password}</td>
                    <td>${testCase.role}</td>
                    <td>${status}</td>
                </tr>`);
            });
            
            results.push('</table>');
            results.push(`<p><strong>Overall Result:</strong> ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}</p>`);
            
            showResult('allAccountsResult', results.join(''), allPassed ? 'success' : 'error');
        }

        function testSessionManagement() {
            let results = ['<h3>Session Management Test Results:</h3>'];
            
            try {
                // Test session creation
                const testUser = VALID_USERS[0];
                const sessionData = {
                    user: testUser,
                    loginTime: new Date().toISOString(),
                    sessionId: 'test-session-' + Date.now()
                };
                
                localStorage.setItem('crmSession', JSON.stringify(sessionData));
                localStorage.setItem('isLoggedIn', 'true');
                
                results.push('<p>✅ Session creation: PASS</p>');
                
                // Test session retrieval
                const retrievedSession = localStorage.getItem('crmSession');
                const parsedSession = JSON.parse(retrievedSession);
                
                if (parsedSession.user.username === testUser.username) {
                    results.push('<p>✅ Session retrieval: PASS</p>');
                } else {
                    results.push('<p>❌ Session retrieval: FAIL</p>');
                }
                
                // Test session cleanup
                localStorage.removeItem('crmSession');
                localStorage.removeItem('isLoggedIn');
                
                results.push('<p>✅ Session cleanup: PASS</p>');
                results.push('<p><strong>Session Management:</strong> ✅ ALL TESTS PASSED</p>');
                
                showResult('sessionResult', results.join(''), 'success');
                
            } catch (error) {
                results.push(`<p>❌ Session management error: ${error.message}</p>`);
                showResult('sessionResult', results.join(''), 'error');
            }
        }

        function testInvalidCredentials() {
            let results = ['<h3>Invalid Credentials Test Results:</h3>'];
            
            const invalidTests = [
                { username: 'admin', password: 'wrong', role: 'manager', desc: 'Wrong password' },
                { username: 'wrong', password: 'admin123', role: 'manager', desc: 'Wrong username' },
                { username: 'admin', password: 'admin123', role: 'wrong', desc: 'Wrong role' },
                { username: '', password: 'admin123', role: 'manager', desc: 'Empty username' },
                { username: 'admin', password: '', role: 'manager', desc: 'Empty password' },
                { username: 'admin', password: 'admin123', role: '', desc: 'Empty role' }
            ];
            
            let allFailed = true;
            
            invalidTests.forEach(test => {
                const result = authenticateUser(test.username, test.password, test.role);
                const status = !result.success ? '✅ CORRECTLY FAILED' : '❌ INCORRECTLY PASSED';
                
                if (result.success) allFailed = false;
                
                results.push(`<p><strong>${test.desc}:</strong> ${status}</p>`);
            });
            
            results.push(`<p><strong>Overall Result:</strong> ${allFailed ? '✅ ALL INVALID TESTS CORRECTLY FAILED' : '❌ SOME INVALID TESTS INCORRECTLY PASSED'}</p>`);
            
            showResult('invalidCredsResult', results.join(''), allFailed ? 'success' : 'error');
        }

        function clearAllData() {
            try {
                localStorage.clear();
                showResult('cleanupResult', '<p>✅ All session data cleared successfully</p>', 'success');
            } catch (error) {
                showResult('cleanupResult', `<p>❌ Error clearing data: ${error.message}</p>`, 'error');
            }
        }

        function resetTests() {
            const resultContainers = [
                'userDatabaseResult',
                'authFunctionResult', 
                'allAccountsResult',
                'sessionResult',
                'invalidCredsResult',
                'cleanupResult'
            ];
            
            resultContainers.forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
            
            showResult('cleanupResult', '<p>✅ All test results reset</p>', 'info');
        }

        // Auto-run basic tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Authentication Test Suite Loaded');
            console.log('Available test functions:', {
                testUserDatabase,
                testAuthenticationFunction,
                testAllUserAccounts,
                testSessionManagement,
                testInvalidCredentials
            });
        });
    </script>
</body>
</html>
