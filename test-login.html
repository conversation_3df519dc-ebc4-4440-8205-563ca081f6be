<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام تسجيل الدخول الجديد</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            max-width: 900px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }
        .header h1 {
            color: #667eea;
            margin-bottom: 10px;
        }
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .test-section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: 500;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        button:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .test-btn {
            background: #28a745;
        }
        .test-btn:hover {
            background: #218838;
        }
        .clear-btn {
            background: #dc3545;
        }
        .clear-btn:hover {
            background: #c82333;
        }
        .open-btn {
            background: #17a2b8;
        }
        .open-btn:hover {
            background: #138496;
        }
        .credentials-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .credentials-table th,
        .credentials-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }
        .credentials-table th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }
        .credentials-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-success {
            background: #28a745;
        }
        .status-error {
            background: #dc3545;
        }
        .status-pending {
            background: #ffc107;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 13px;
            border: 1px solid #e9ecef;
        }
        .quick-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        @media (max-width: 768px) {
            .test-container {
                margin: 10px;
                padding: 20px;
            }
            .quick-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🔐 اختبار نظام تسجيل الدخول الجديد</h1>
            <p>هذه الصفحة تختبر جميع وظائف نظام المصادقة الجديد للتأكد من عمله بشكل صحيح</p>
        </div>
        
        <div class="test-section">
            <h2>1. بيانات المستخدمين المتاحة</h2>
            <p>هذه هي بيانات الدخول الصحيحة التي يجب أن تعمل:</p>
            <table class="credentials-table">
                <thead>
                    <tr>
                        <th>الدور</th>
                        <th>اسم المستخدم</th>
                        <th>كلمة المرور</th>
                        <th>قيمة الدور</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>مدير النظام</td>
                        <td>admin</td>
                        <td>admin123</td>
                        <td>manager</td>
                        <td><span class="status-indicator status-success"></span>جاهز</td>
                    </tr>
                    <tr>
                        <td>مشرف المبيعات</td>
                        <td>supervisor</td>
                        <td>super123</td>
                        <td>supervisor</td>
                        <td><span class="status-indicator status-success"></span>جاهز</td>
                    </tr>
                    <tr>
                        <td>موظف المبيعات</td>
                        <td>sales</td>
                        <td>sales123</td>
                        <td>telesales</td>
                        <td><span class="status-indicator status-success"></span>جاهز</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="test-section">
            <h2>2. اختبار وظائف المصادقة</h2>
            <p>اختبار جميع وظائف نظام المصادقة:</p>
            <div class="quick-actions">
                <button class="test-btn" onclick="testUserDatabase()">اختبار قاعدة بيانات المستخدمين</button>
                <button class="test-btn" onclick="testValidCredentials()">اختبار البيانات الصحيحة</button>
                <button class="test-btn" onclick="testInvalidCredentials()">اختبار البيانات الخاطئة</button>
                <button class="test-btn" onclick="testSessionManagement()">اختبار إدارة الجلسات</button>
            </div>
            <div id="testResults"></div>
        </div>
        
        <div class="test-section">
            <h2>3. اختبار تسجيل الدخول المباشر</h2>
            <p>اختبار تسجيل الدخول الفعلي مع كل حساب:</p>
            <div class="quick-actions">
                <button class="test-btn" onclick="testDirectLogin('admin', 'admin123', 'manager')">اختبار حساب المدير</button>
                <button class="test-btn" onclick="testDirectLogin('supervisor', 'super123', 'supervisor')">اختبار حساب المشرف</button>
                <button class="test-btn" onclick="testDirectLogin('sales', 'sales123', 'telesales')">اختبار حساب المبيعات</button>
            </div>
            <div id="directTestResults"></div>
        </div>
        
        <div class="test-section">
            <h2>4. فتح صفحات النظام</h2>
            <p>فتح صفحات النظام للاختبار اليدوي:</p>
            <div class="quick-actions">
                <button class="open-btn" onclick="openLoginPage()">فتح صفحة تسجيل الدخول</button>
                <button class="open-btn" onclick="openDashboard()">فتح لوحة التحكم</button>
                <button class="open-btn" onclick="checkCurrentSession()">فحص الجلسة الحالية</button>
            </div>
            <div id="pageResults"></div>
        </div>
        
        <div class="test-section">
            <h2>5. إدارة البيانات</h2>
            <p>إدارة بيانات الجلسة والاختبارات:</p>
            <div class="quick-actions">
                <button class="clear-btn" onclick="clearAllData()">مسح جميع البيانات</button>
                <button class="clear-btn" onclick="clearTestResults()">مسح نتائج الاختبارات</button>
                <button onclick="showStorageData()">عرض بيانات التخزين</button>
            </div>
            <div id="managementResults"></div>
        </div>
    </div>

    <script>
        // نسخ نظام المصادقة من صفحة تسجيل الدخول
        const USERS = [
            {
                username: 'admin',
                password: 'admin123',
                role: 'manager',
                name: 'مدير النظام',
                id: 1
            },
            {
                username: 'supervisor',
                password: 'super123',
                role: 'supervisor',
                name: 'مشرف المبيعات',
                id: 2
            },
            {
                username: 'sales',
                password: 'sales123',
                role: 'telesales',
                name: 'موظف المبيعات',
                id: 3
            }
        ];

        // عرض النتائج
        function showResult(containerId, content, type = 'info') {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="test-result ${type}">${content}</div>`;
        }

        // التحقق من صحة بيانات الدخول
        function validateCredentials(username, password, role) {
            const user = USERS.find(u => 
                u.username === username && 
                u.password === password && 
                u.role === role
            );
            return user || null;
        }

        // اختبار قاعدة بيانات المستخدمين
        function testUserDatabase() {
            let results = ['<h3>نتائج اختبار قاعدة بيانات المستخدمين:</h3>'];
            
            results.push(`<p><strong>عدد المستخدمين:</strong> ${USERS.length}</p>`);
            
            let allValid = true;
            USERS.forEach((user, index) => {
                const isValid = user.username && user.password && user.role && user.name && user.id;
                if (!isValid) allValid = false;
                
                results.push(`<p>${index + 1}. ${user.name}: ${isValid ? '✅ صحيح' : '❌ خطأ'}</p>`);
            });
            
            results.push(`<p><strong>النتيجة الإجمالية:</strong> ${allValid ? '✅ جميع البيانات صحيحة' : '❌ يوجد أخطاء في البيانات'}</p>`);
            
            showResult('testResults', results.join(''), allValid ? 'success' : 'error');
        }

        // اختبار البيانات الصحيحة
        function testValidCredentials() {
            let results = ['<h3>نتائج اختبار البيانات الصحيحة:</h3>'];
            
            const testCases = [
                { username: 'admin', password: 'admin123', role: 'manager', name: 'مدير النظام' },
                { username: 'supervisor', password: 'super123', role: 'supervisor', name: 'مشرف المبيعات' },
                { username: 'sales', password: 'sales123', role: 'telesales', name: 'موظف المبيعات' }
            ];
            
            let allPassed = true;
            testCases.forEach(testCase => {
                const user = validateCredentials(testCase.username, testCase.password, testCase.role);
                const passed = user && user.name === testCase.name;
                if (!passed) allPassed = false;
                
                results.push(`<p>${testCase.name}: ${passed ? '✅ نجح' : '❌ فشل'}</p>`);
            });
            
            results.push(`<p><strong>النتيجة الإجمالية:</strong> ${allPassed ? '✅ جميع الاختبارات نجحت' : '❌ بعض الاختبارات فشلت'}</p>`);
            
            showResult('testResults', results.join(''), allPassed ? 'success' : 'error');
        }

        // اختبار البيانات الخاطئة
        function testInvalidCredentials() {
            let results = ['<h3>نتائج اختبار البيانات الخاطئة:</h3>'];
            
            const invalidCases = [
                { username: 'wrong', password: 'admin123', role: 'manager', desc: 'اسم مستخدم خاطئ' },
                { username: 'admin', password: 'wrong', role: 'manager', desc: 'كلمة مرور خاطئة' },
                { username: 'admin', password: 'admin123', role: 'wrong', desc: 'دور خاطئ' },
                { username: '', password: 'admin123', role: 'manager', desc: 'اسم مستخدم فارغ' },
                { username: 'admin', password: '', role: 'manager', desc: 'كلمة مرور فارغة' }
            ];
            
            let allFailed = true;
            invalidCases.forEach(testCase => {
                const user = validateCredentials(testCase.username, testCase.password, testCase.role);
                const correctlyFailed = !user;
                if (!correctlyFailed) allFailed = false;
                
                results.push(`<p>${testCase.desc}: ${correctlyFailed ? '✅ فشل بشكل صحيح' : '❌ نجح خطأً'}</p>`);
            });
            
            results.push(`<p><strong>النتيجة الإجمالية:</strong> ${allFailed ? '✅ جميع البيانات الخاطئة فشلت بشكل صحيح' : '❌ بعض البيانات الخاطئة نجحت خطأً'}</p>`);
            
            showResult('testResults', results.join(''), allFailed ? 'success' : 'error');
        }

        // اختبار إدارة الجلسات
        function testSessionManagement() {
            let results = ['<h3>نتائج اختبار إدارة الجلسات:</h3>'];
            
            try {
                // اختبار إنشاء الجلسة
                const testUser = USERS[0];
                const sessionData = {
                    user: testUser,
                    loginTime: new Date().toISOString(),
                    sessionId: 'test_session_' + Date.now()
                };
                
                localStorage.setItem('userSession', JSON.stringify(sessionData));
                localStorage.setItem('isLoggedIn', 'true');
                
                results.push('<p>✅ إنشاء الجلسة: نجح</p>');
                
                // اختبار استرجاع الجلسة
                const retrievedSession = localStorage.getItem('userSession');
                const parsedSession = JSON.parse(retrievedSession);
                
                if (parsedSession.user.username === testUser.username) {
                    results.push('<p>✅ استرجاع الجلسة: نجح</p>');
                } else {
                    results.push('<p>❌ استرجاع الجلسة: فشل</p>');
                }
                
                // اختبار مسح الجلسة
                localStorage.removeItem('userSession');
                localStorage.removeItem('isLoggedIn');
                
                results.push('<p>✅ مسح الجلسة: نجح</p>');
                results.push('<p><strong>إدارة الجلسات:</strong> ✅ جميع الاختبارات نجحت</p>');
                
                showResult('testResults', results.join(''), 'success');
                
            } catch (error) {
                results.push(`<p>❌ خطأ في إدارة الجلسات: ${error.message}</p>`);
                showResult('testResults', results.join(''), 'error');
            }
        }

        // اختبار تسجيل الدخول المباشر
        function testDirectLogin(username, password, role) {
            const user = validateCredentials(username, password, role);
            
            if (user) {
                // حفظ الجلسة
                const sessionData = {
                    user: user,
                    loginTime: new Date().toISOString(),
                    sessionId: 'direct_test_' + Date.now()
                };
                
                localStorage.setItem('userSession', JSON.stringify(sessionData));
                localStorage.setItem('isLoggedIn', 'true');
                
                showResult('directTestResults', 
                    `<h3>✅ نجح تسجيل الدخول</h3>
                     <p><strong>المستخدم:</strong> ${user.name}</p>
                     <p><strong>الدور:</strong> ${user.role}</p>
                     <p><strong>وقت الدخول:</strong> ${new Date().toLocaleString('ar')}</p>
                     <p>يمكنك الآن فتح لوحة التحكم للتأكد من عمل النظام.</p>`, 
                    'success');
            } else {
                showResult('directTestResults', 
                    `<h3>❌ فشل تسجيل الدخول</h3>
                     <p>البيانات المستخدمة: ${username}/${password}/${role}</p>
                     <p>هذا يشير إلى وجود مشكلة في نظام المصادقة.</p>`, 
                    'error');
            }
        }

        // فتح صفحة تسجيل الدخول
        function openLoginPage() {
            window.open('login.html', '_blank');
            showResult('pageResults', '<p>✅ تم فتح صفحة تسجيل الدخول في نافذة جديدة</p>', 'info');
        }

        // فتح لوحة التحكم
        function openDashboard() {
            window.open('dashboard.html', '_blank');
            showResult('pageResults', '<p>✅ تم فتح لوحة التحكم في نافذة جديدة</p>', 'info');
        }

        // فحص الجلسة الحالية
        function checkCurrentSession() {
            try {
                const isLoggedIn = localStorage.getItem('isLoggedIn');
                const sessionData = localStorage.getItem('userSession');
                
                if (isLoggedIn === 'true' && sessionData) {
                    const session = JSON.parse(sessionData);
                    showResult('pageResults', 
                        `<h3>✅ يوجد جلسة نشطة</h3>
                         <p><strong>المستخدم:</strong> ${session.user.name}</p>
                         <p><strong>الدور:</strong> ${session.user.role}</p>
                         <p><strong>وقت الدخول:</strong> ${new Date(session.loginTime).toLocaleString('ar')}</p>`, 
                        'success');
                } else {
                    showResult('pageResults', '<p>❌ لا توجد جلسة نشطة</p>', 'warning');
                }
            } catch (error) {
                showResult('pageResults', `<p>❌ خطأ في فحص الجلسة: ${error.message}</p>`, 'error');
            }
        }

        // مسح جميع البيانات
        function clearAllData() {
            try {
                localStorage.clear();
                showResult('managementResults', '<p>✅ تم مسح جميع البيانات بنجاح</p>', 'success');
            } catch (error) {
                showResult('managementResults', `<p>❌ خطأ في مسح البيانات: ${error.message}</p>`, 'error');
            }
        }

        // مسح نتائج الاختبارات
        function clearTestResults() {
            const resultContainers = ['testResults', 'directTestResults', 'pageResults', 'managementResults'];
            resultContainers.forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
            showResult('managementResults', '<p>✅ تم مسح جميع نتائج الاختبارات</p>', 'info');
        }

        // عرض بيانات التخزين
        function showStorageData() {
            let results = ['<h3>بيانات التخزين المحلي:</h3>'];
            
            if (localStorage.length === 0) {
                results.push('<p>لا توجد بيانات محفوظة</p>');
            } else {
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    const value = localStorage.getItem(key);
                    results.push(`<p><strong>${key}:</strong> ${value.substring(0, 100)}${value.length > 100 ? '...' : ''}</p>`);
                }
            }
            
            showResult('managementResults', results.join(''), 'info');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('صفحة اختبار نظام تسجيل الدخول جاهزة');
            console.log('المستخدمون المتاحون:', USERS.map(u => `${u.username}/${u.role}`));
        });
    </script>
</body>
</html>
