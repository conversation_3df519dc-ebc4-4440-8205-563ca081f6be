<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - CRM Dashboard</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="dashboard-page">
    <!-- Header -->
    <header class="main-header">
        <div class="header-content">
            <div class="header-left">
                <button id="sidebarToggle" class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <i class="fas fa-users-cog"></i>
                    <span data-key="app_title">نظام إدارة علاقات العملاء</span>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="globalSearch" placeholder="البحث في جميع البيانات..." data-key-placeholder="global_search">
                        <div id="searchResults" class="search-results"></div>
                    </div>
                </div>
            </div>
            
            <div class="header-right">
                <div class="header-actions">
                    <button id="languageToggle" class="btn-icon" title="تغيير اللغة">
                        <i class="fas fa-globe"></i>
                    </button>
                    
                    <button id="exportData" class="btn-icon" title="تصدير البيانات">
                        <i class="fas fa-download"></i>
                    </button>
                    
                    <button id="backupData" class="btn-icon" title="نسخ احتياطي">
                        <i class="fas fa-database"></i>
                    </button>
                    
                    <div class="user-menu">
                        <button class="user-profile" id="userProfile">
                            <i class="fas fa-user-circle"></i>
                            <span id="currentUserName">المستخدم</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="user-dropdown" id="userDropdown">
                            <a href="#" id="userSettings">
                                <i class="fas fa-cog"></i>
                                <span data-key="settings">الإعدادات</span>
                            </a>
                            <a href="#" id="logoutBtn">
                                <i class="fas fa-sign-out-alt"></i>
                                <span data-key="logout">تسجيل الخروج</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <nav class="sidebar-nav">
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#dashboard" class="nav-link active" data-module="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span data-key="dashboard">لوحة التحكم</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="#customers" class="nav-link" data-module="customers">
                        <i class="fas fa-users"></i>
                        <span data-key="customers">العملاء</span>
                        <span class="nav-badge" id="customerCount">0</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="#suppliers" class="nav-link" data-module="suppliers">
                        <i class="fas fa-truck"></i>
                        <span data-key="suppliers">الموردين</span>
                        <span class="nav-badge" id="supplierCount">0</span>
                    </a>
                </li>
                
                <li class="nav-item" id="telesalesNav">
                    <a href="#telesales" class="nav-link" data-module="telesales">
                        <i class="fas fa-headset"></i>
                        <span data-key="telesales_team">فريق المبيعات</span>
                        <span class="nav-badge" id="telesalesCount">0</span>
                    </a>
                </li>
                
                <li class="nav-item" id="reportsNav">
                    <a href="#reports" class="nav-link" data-module="reports">
                        <i class="fas fa-chart-bar"></i>
                        <span data-key="reports">التقارير</span>
                    </a>
                </li>
                
                <li class="nav-item" id="settingsNav">
                    <a href="#settings" class="nav-link" data-module="settings">
                        <i class="fas fa-cogs"></i>
                        <span data-key="system_settings">إعدادات النظام</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content" id="mainContent">
        <!-- Dashboard Module -->
        <div id="dashboardModule" class="module active">
            <div class="module-header">
                <h2 data-key="dashboard">لوحة التحكم</h2>
                <div class="module-actions">
                    <span class="last-update" id="lastUpdate">آخر تحديث: الآن</span>
                </div>
            </div>
            
            <div class="dashboard-stats">
                <div class="stat-card">
                    <div class="stat-icon customers">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalCustomers">0</h3>
                        <p data-key="total_customers">إجمالي العملاء</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon suppliers">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalSuppliers">0</h3>
                        <p data-key="total_suppliers">إجمالي الموردين</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon telesales">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalTelesales">0</h3>
                        <p data-key="total_telesales">فريق المبيعات</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon tasks">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalTasks">0</h3>
                        <p data-key="active_tasks">المهام النشطة</p>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-content">
                <div class="dashboard-grid">
                    <div class="dashboard-widget">
                        <h3 data-key="recent_customers">العملاء الجدد</h3>
                        <div id="recentCustomers" class="widget-content">
                            <!-- Recent customers will be loaded here -->
                        </div>
                    </div>
                    
                    <div class="dashboard-widget">
                        <h3 data-key="pending_tasks">المهام المعلقة</h3>
                        <div id="pendingTasks" class="widget-content">
                            <!-- Pending tasks will be loaded here -->
                        </div>
                    </div>
                    
                    <div class="dashboard-widget">
                        <h3 data-key="performance_overview">نظرة عامة على الأداء</h3>
                        <div id="performanceOverview" class="widget-content">
                            <!-- Performance data will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Other modules will be loaded dynamically -->
        <div id="customersModule" class="module"></div>
        <div id="suppliersModule" class="module"></div>
        <div id="telesalesModule" class="module"></div>
        <div id="reportsModule" class="module"></div>
        <div id="settingsModule" class="module"></div>
    </main>

    <!-- Modals and Overlays -->
    <div id="modalOverlay" class="modal-overlay"></div>
    
    <!-- Notification Container -->
    <div id="notificationContainer" class="notification-container"></div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="js/language.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/search.js"></script>
    <script src="js/export.js"></script>
    <script src="js/customers.js"></script>
    <script src="js/suppliers.js"></script>
    <script src="js/telesales.js"></script>
    <script src="js/app.js"></script>

    <!-- نظام المصادقة المحدث لـ CRM -->
    <script>
        // متغيرات النظام
        let currentUser = null;
        let sessionData = null;

        // التحقق من المصادقة المحدث
        function checkAuthentication() {
            try {
                const isLoggedIn = localStorage.getItem('crmLoggedIn');
                const sessionDataStr = localStorage.getItem('crmSession');

                if (isLoggedIn !== 'true' || !sessionDataStr) {
                    redirectToLogin('لم يتم تسجيل الدخول');
                    return false;
                }

                sessionData = JSON.parse(sessionDataStr);

                // التحقق من صحة بيانات الجلسة
                if (!sessionData.user || !sessionData.user.username || !sessionData.expiresAt) {
                    redirectToLogin('بيانات الجلسة غير صحيحة');
                    return false;
                }

                // التحقق من انتهاء صلاحية الجلسة
                const expiryDate = new Date(sessionData.expiresAt);
                const currentDate = new Date();

                if (expiryDate <= currentDate) {
                    redirectToLogin('انتهت صلاحية الجلسة');
                    return false;
                }

                // حفظ بيانات المستخدم الحالي
                currentUser = sessionData.user;

                // تحديث واجهة المستخدم
                updateUserInterface();

                // تحديث آخر نشاط
                localStorage.setItem('crmLastActivity', new Date().toISOString());

                return true;
            } catch (error) {
                console.error('خطأ في التحقق من المصادقة:', error);
                redirectToLogin('خطأ في النظام');
                return false;
            }
        }

        // تحديث واجهة المستخدم
        function updateUserInterface() {
            if (!currentUser) return;

            // تحديث اسم المستخدم
            const userNameElements = document.querySelectorAll('#currentUserName, .user-name, .username');
            userNameElements.forEach(element => {
                if (element) {
                    element.textContent = currentUser.name;
                }
            });

            // تحديث معلومات إضافية
            const userRoleElements = document.querySelectorAll('.user-role');
            userRoleElements.forEach(element => {
                if (element) {
                    element.textContent = getRoleDisplayName(currentUser.role);
                }
            });

            // إظهار/إخفاء عناصر حسب الصلاحيات
            updatePermissionBasedUI();
        }

        // الحصول على اسم الدور للعرض
        function getRoleDisplayName(role) {
            const roleNames = {
                'manager': 'مدير النظام',
                'supervisor': 'مشرف المبيعات',
                'telesales': 'موظف المبيعات الهاتفية'
            };
            return roleNames[role] || role;
        }

        // تحديث الواجهة حسب الصلاحيات
        function updatePermissionBasedUI() {
            if (!currentUser || !currentUser.permissions) return;

            const hasAllPermissions = currentUser.permissions.includes('all');
            const canManageTeam = currentUser.permissions.includes('manage_team');

            // إظهار/إخفاء عناصر الإدارة
            const adminElements = document.querySelectorAll('.admin-only');
            adminElements.forEach(element => {
                element.style.display = hasAllPermissions ? 'block' : 'none';
            });

            // إظهار/إخفاء عناصر إدارة الفريق
            const teamElements = document.querySelectorAll('.team-management');
            teamElements.forEach(element => {
                element.style.display = (hasAllPermissions || canManageTeam) ? 'block' : 'none';
            });
        }

        // إعادة التوجيه إلى صفحة تسجيل الدخول
        function redirectToLogin(reason = '') {
            console.log('إعادة توجيه إلى تسجيل الدخول:', reason);

            // مسح جميع بيانات الجلسة
            localStorage.removeItem('crmLoggedIn');
            localStorage.removeItem('crmSession');
            localStorage.removeItem('crmLastActivity');

            // مسح البيانات القديمة أيضاً
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('userSession');

            // إعادة التوجيه
            window.location.href = 'login.html';
        }

        // تسجيل الخروج المحدث
        function performLogout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج من النظام؟')) {
                console.log('تسجيل خروج المستخدم:', currentUser?.name);
                redirectToLogin('تسجيل خروج');
            }
        }

        // تحديث آخر نشاط
        function updateLastActivity() {
            if (localStorage.getItem('crmLoggedIn') === 'true') {
                localStorage.setItem('crmLastActivity', new Date().toISOString());
            }
        }

        // مراقبة النشاط
        function setupActivityMonitoring() {
            // تحديث آخر نشاط عند التفاعل
            ['click', 'keypress', 'mousemove', 'scroll'].forEach(eventType => {
                document.addEventListener(eventType, updateLastActivity, { passive: true });
            });

            // فحص انتهاء الجلسة كل دقيقة
            setInterval(() => {
                if (sessionData && sessionData.expiresAt) {
                    const expiryDate = new Date(sessionData.expiresAt);
                    const currentDate = new Date();

                    if (expiryDate <= currentDate) {
                        alert('انتهت صلاحية الجلسة. سيتم إعادة توجيهك إلى صفحة تسجيل الدخول.');
                        redirectToLogin('انتهت صلاحية الجلسة');
                    }
                }
            }, 60000); // كل دقيقة
        }

        // تهيئة لوحة التحكم
        function initializeDashboard() {
            try {
                // التحقق من المصادقة أولاً
                if (!checkAuthentication()) {
                    return;
                }

                // ربط أزرار تسجيل الخروج
                const logoutButtons = document.querySelectorAll('#logoutBtn, .logout-btn, .btn-logout');
                logoutButtons.forEach(button => {
                    if (button) {
                        button.addEventListener('click', function(e) {
                            e.preventDefault();
                            performLogout();
                        });
                    }
                });

                // إعداد مراقبة النشاط
                setupActivityMonitoring();

                // تهيئة مكونات النظام
                initializeSystemComponents();

                console.log('تم تحميل لوحة التحكم بنجاح للمستخدم:', currentUser.name);

            } catch (error) {
                console.error('خطأ في تهيئة لوحة التحكم:', error);
                redirectToLogin('خطأ في التهيئة');
            }
        }

        // تهيئة مكونات النظام
        function initializeSystemComponents() {
            try {
                // تهيئة نظام اللغة
                if (typeof initializeLanguage === 'function') {
                    initializeLanguage();
                }

                // تهيئة نظام المصادقة
                if (typeof initializeAuth === 'function') {
                    initializeAuth();
                }

                // تهيئة تطبيق CRM
                if (typeof CRMApplication === 'function') {
                    window.app = new CRMApplication();
                    console.log('تم تهيئة تطبيق CRM بنجاح');
                }

            } catch (error) {
                console.error('خطأ في تهيئة مكونات النظام:', error);
            }
        }

        // تهيئة الصفحة عند التحميل
        document.addEventListener('DOMContentLoaded', initializeDashboard);

        // معالج الأخطاء العام
        window.addEventListener('error', function(e) {
            console.error('خطأ عام في لوحة التحكم:', e.error);
        });

        // معالج إغلاق النافذة
        window.addEventListener('beforeunload', function() {
            updateLastActivity();
        });
    </script>
</body>
</html>
