// Local Storage Management System for CRM
class StorageManager {
    constructor() {
        this.storageKeys = {
            customers: 'crm_customers',
            suppliers: 'crm_suppliers',
            telesales: 'crm_telesales',
            tasks: 'crm_tasks',
            users: 'crm_users',
            settings: 'crm_settings',
            backup: 'crm_backup'
        };
        
        this.initializeDefaultData();
    }
    
    // Initialize default data if not exists
    initializeDefaultData() {
        // Initialize empty arrays if not exists
        if (!this.getCustomers()) {
            this.saveCustomers([]);
        }
        
        if (!this.getSuppliers()) {
            this.saveSuppliers([]);
        }
        
        if (!this.getTelesales()) {
            this.saveTelesales([]);
        }
        
        if (!this.getTasks()) {
            this.saveTasks([]);
        }
        
        // Initialize default users if not exists
        if (!this.getUsers()) {
            this.initializeDefaultUsers();
        }
        
        // Initialize default settings
        if (!this.getSettings()) {
            this.initializeDefaultSettings();
        }
    }
    
    // Initialize default users for demo
    initializeDefaultUsers() {
        const defaultUsers = [
            {
                id: 1,
                username: 'admin',
                password: 'admin123', // In production, this should be hashed
                role: 'manager',
                name: 'مدير النظام',
                email: '<EMAIL>',
                active: true,
                createdAt: new Date().toISOString()
            },
            {
                id: 2,
                username: 'supervisor',
                password: 'super123',
                role: 'supervisor',
                name: 'مشرف المبيعات',
                email: '<EMAIL>',
                active: true,
                createdAt: new Date().toISOString()
            },
            {
                id: 3,
                username: 'sales',
                password: 'sales123',
                role: 'telesales',
                name: 'موظف المبيعات',
                email: '<EMAIL>',
                active: true,
                createdAt: new Date().toISOString()
            }
        ];
        
        this.saveUsers(defaultUsers);
    }
    
    // Initialize default settings
    initializeDefaultSettings() {
        const defaultSettings = {
            language: 'ar',
            theme: 'light',
            dateFormat: 'DD/MM/YYYY',
            timeFormat: '24h',
            pageSize: 10,
            autoBackup: true,
            backupInterval: 7, // days
            notifications: true,
            soundEnabled: true,
            companyName: 'شركة إدارة علاقات العملاء',
            companyLogo: '',
            currency: 'SAR',
            timezone: 'Asia/Riyadh'
        };

        this.saveSettings(defaultSettings);

        // Initialize demo data if no data exists
        this.initializeDemoData();
    }

    // Initialize demo data for testing
    initializeDemoData() {
        // Only add demo data if no existing data
        if (this.getCustomers().length === 0) {
            this.addDemoCustomers();
        }

        if (this.getSuppliers().length === 0) {
            this.addDemoSuppliers();
        }

        if (this.getTelesales().length === 0) {
            this.addDemoTelesales();
        }

        if (this.getTasks().length === 0) {
            this.addDemoTasks();
        }
    }

    addDemoCustomers() {
        const demoCustomers = [
            {
                fullName: 'أحمد محمد العلي',
                mobilePhone: '+966501234567',
                email: '<EMAIL>',
                city: 'الرياض',
                businessType: 'تجارة إلكترونية',
                status: 'active',
                lastContactDate: '2024-06-10',
                notes: 'عميل مهم، يحتاج متابعة دورية'
            },
            {
                fullName: 'فاطمة سعد الحربي',
                mobilePhone: '+966502345678',
                email: '<EMAIL>',
                city: 'جدة',
                businessType: 'مطاعم',
                status: 'prospect',
                lastContactDate: '2024-06-12',
                notes: 'مهتمة بالخدمات، تحتاج عرض سعر'
            },
            {
                fullName: 'خالد عبدالله القحطاني',
                mobilePhone: '+966503456789',
                email: '<EMAIL>',
                city: 'الدمام',
                businessType: 'خدمات طبية',
                status: 'active',
                lastContactDate: '2024-06-08',
                notes: 'عميل منتظم، راضي عن الخدمة'
            },
            {
                fullName: 'نورا إبراهيم الشمري',
                mobilePhone: '+966504567890',
                email: '<EMAIL>',
                city: 'الرياض',
                businessType: 'تعليم',
                status: 'inactive',
                lastContactDate: '2024-05-20',
                notes: 'لم تتفاعل مؤخراً، تحتاج متابعة'
            },
            {
                fullName: 'محمد سالم الغامدي',
                mobilePhone: '+966505678901',
                email: '<EMAIL>',
                city: 'مكة المكرمة',
                businessType: 'سياحة وسفر',
                status: 'prospect',
                lastContactDate: '2024-06-14',
                notes: 'عميل محتمل، يدرس العروض'
            }
        ];

        demoCustomers.forEach(customer => this.addCustomer(customer));
    }

    addDemoSuppliers() {
        const demoSuppliers = [
            {
                supplierName: 'شركة التقنية المتقدمة',
                serviceType: 'خدمات تقنية',
                contactMethod: 'both',
                phone: '+966112345678',
                email: '<EMAIL>',
                city: 'الرياض',
                address: 'حي العليا، شارع الملك فهد',
                notes: 'مورد موثوق للحلول التقنية'
            },
            {
                supplierName: 'مؤسسة الخدمات اللوجستية',
                serviceType: 'شحن وتوصيل',
                contactMethod: 'phone',
                phone: '+966123456789',
                email: '<EMAIL>',
                city: 'جدة',
                address: 'المنطقة الصناعية، جدة',
                notes: 'خدمات شحن سريعة وموثوقة'
            },
            {
                supplierName: 'شركة التسويق الرقمي',
                serviceType: 'تسويق وإعلان',
                contactMethod: 'email',
                phone: '+966134567890',
                email: '<EMAIL>',
                city: 'الدمام',
                address: 'برج الأعمال، الدمام',
                notes: 'متخصصون في التسويق الإلكتروني'
            }
        ];

        demoSuppliers.forEach(supplier => this.addSupplier(supplier));
    }

    addDemoTelesales() {
        const demoTelesales = [
            {
                employeeName: 'سارة أحمد المطيري',
                employeeId: 'EMP001',
                position: 'مندوب مبيعات أول',
                department: 'المبيعات',
                hireDate: '2023-01-15',
                email: '<EMAIL>',
                phone: '+966501111111',
                active: true
            },
            {
                employeeName: 'عبدالرحمن محمد الزهراني',
                employeeId: 'EMP002',
                position: 'مندوب مبيعات',
                department: 'المبيعات',
                hireDate: '2023-03-20',
                email: '<EMAIL>',
                phone: '+966502222222',
                active: true
            },
            {
                employeeName: 'ريم سعد العتيبي',
                employeeId: 'EMP003',
                position: 'مشرف مبيعات',
                department: 'المبيعات',
                hireDate: '2022-08-10',
                email: '<EMAIL>',
                phone: '+966503333333',
                active: true
            }
        ];

        demoTelesales.forEach(employee => this.addTelesales(employee));
    }

    addDemoTasks() {
        const telesales = this.getTelesales();
        if (telesales.length === 0) return;

        const demoTasks = [
            {
                taskTitle: 'متابعة العملاء الجدد',
                taskDescription: 'الاتصال بالعملاء الجدد والترحيب بهم وشرح الخدمات',
                assignedTo: telesales[0].id,
                priority: 'high',
                status: 'pending',
                deadline: '2024-06-20',
                createdBy: 'manager'
            },
            {
                taskTitle: 'تحديث بيانات العملاء',
                taskDescription: 'مراجعة وتحديث بيانات العملاء في النظام',
                assignedTo: telesales[1].id,
                priority: 'medium',
                status: 'in_progress',
                deadline: '2024-06-18',
                createdBy: 'supervisor'
            },
            {
                taskTitle: 'إعداد تقرير المبيعات الأسبوعي',
                taskDescription: 'جمع بيانات المبيعات وإعداد التقرير الأسبوعي',
                assignedTo: telesales[2].id,
                priority: 'high',
                status: 'completed',
                deadline: '2024-06-15',
                createdBy: 'manager'
            },
            {
                taskTitle: 'الاتصال بالعملاء المحتملين',
                taskDescription: 'التواصل مع قائمة العملاء المحتملين الجديدة',
                assignedTo: telesales[0].id,
                priority: 'medium',
                status: 'pending',
                deadline: '2024-06-22',
                createdBy: 'supervisor'
            }
        ];

        demoTasks.forEach(task => this.addTask(task));
    }
    
    // Generic storage methods
    setItem(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Error saving to localStorage:', error);
            return false;
        }
    }
    
    getItem(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return null;
        }
    }
    
    removeItem(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('Error removing from localStorage:', error);
            return false;
        }
    }
    
    // Customer management
    getCustomers() {
        return this.getItem(this.storageKeys.customers) || [];
    }
    
    saveCustomers(customers) {
        return this.setItem(this.storageKeys.customers, customers);
    }
    
    addCustomer(customer) {
        const customers = this.getCustomers();
        customer.id = this.generateId();
        customer.createdAt = new Date().toISOString();
        customer.updatedAt = new Date().toISOString();
        customers.push(customer);
        return this.saveCustomers(customers);
    }
    
    updateCustomer(customerId, updatedCustomer) {
        const customers = this.getCustomers();
        const index = customers.findIndex(c => c.id === customerId);
        if (index !== -1) {
            customers[index] = { ...customers[index], ...updatedCustomer, updatedAt: new Date().toISOString() };
            return this.saveCustomers(customers);
        }
        return false;
    }
    
    deleteCustomer(customerId) {
        const customers = this.getCustomers();
        const filteredCustomers = customers.filter(c => c.id !== customerId);
        return this.saveCustomers(filteredCustomers);
    }
    
    getCustomerById(customerId) {
        const customers = this.getCustomers();
        return customers.find(c => c.id === customerId);
    }
    
    // Supplier management
    getSuppliers() {
        return this.getItem(this.storageKeys.suppliers) || [];
    }
    
    saveSuppliers(suppliers) {
        return this.setItem(this.storageKeys.suppliers, suppliers);
    }
    
    addSupplier(supplier) {
        const suppliers = this.getSuppliers();
        supplier.id = this.generateId();
        supplier.createdAt = new Date().toISOString();
        supplier.updatedAt = new Date().toISOString();
        suppliers.push(supplier);
        return this.saveSuppliers(suppliers);
    }
    
    updateSupplier(supplierId, updatedSupplier) {
        const suppliers = this.getSuppliers();
        const index = suppliers.findIndex(s => s.id === supplierId);
        if (index !== -1) {
            suppliers[index] = { ...suppliers[index], ...updatedSupplier, updatedAt: new Date().toISOString() };
            return this.saveSuppliers(suppliers);
        }
        return false;
    }
    
    deleteSupplier(supplierId) {
        const suppliers = this.getSuppliers();
        const filteredSuppliers = suppliers.filter(s => s.id !== supplierId);
        return this.saveSuppliers(filteredSuppliers);
    }
    
    getSupplierById(supplierId) {
        const suppliers = this.getSuppliers();
        return suppliers.find(s => s.id === supplierId);
    }
    
    // Telesales management
    getTelesales() {
        return this.getItem(this.storageKeys.telesales) || [];
    }
    
    saveTelesales(telesales) {
        return this.setItem(this.storageKeys.telesales, telesales);
    }
    
    addTelesales(employee) {
        const telesales = this.getTelesales();
        employee.id = this.generateId();
        employee.createdAt = new Date().toISOString();
        employee.updatedAt = new Date().toISOString();
        telesales.push(employee);
        return this.saveTelesales(telesales);
    }
    
    updateTelesales(employeeId, updatedEmployee) {
        const telesales = this.getTelesales();
        const index = telesales.findIndex(e => e.id === employeeId);
        if (index !== -1) {
            telesales[index] = { ...telesales[index], ...updatedEmployee, updatedAt: new Date().toISOString() };
            return this.saveTelesales(telesales);
        }
        return false;
    }
    
    deleteTelesales(employeeId) {
        const telesales = this.getTelesales();
        const filteredTelesales = telesales.filter(e => e.id !== employeeId);
        return this.saveTelesales(filteredTelesales);
    }
    
    getTelesalesById(employeeId) {
        const telesales = this.getTelesales();
        return telesales.find(e => e.id === employeeId);
    }
    
    // Task management
    getTasks() {
        return this.getItem(this.storageKeys.tasks) || [];
    }
    
    saveTasks(tasks) {
        return this.setItem(this.storageKeys.tasks, tasks);
    }
    
    addTask(task) {
        const tasks = this.getTasks();
        task.id = this.generateId();
        task.createdAt = new Date().toISOString();
        task.updatedAt = new Date().toISOString();
        tasks.push(task);
        return this.saveTasks(tasks);
    }
    
    updateTask(taskId, updatedTask) {
        const tasks = this.getTasks();
        const index = tasks.findIndex(t => t.id === taskId);
        if (index !== -1) {
            tasks[index] = { ...tasks[index], ...updatedTask, updatedAt: new Date().toISOString() };
            return this.saveTasks(tasks);
        }
        return false;
    }
    
    deleteTask(taskId) {
        const tasks = this.getTasks();
        const filteredTasks = tasks.filter(t => t.id !== taskId);
        return this.saveTasks(filteredTasks);
    }
    
    getTaskById(taskId) {
        const tasks = this.getTasks();
        return tasks.find(t => t.id === taskId);
    }
    
    getTasksByEmployee(employeeId) {
        const tasks = this.getTasks();
        return tasks.filter(t => t.assignedTo === employeeId);
    }
    
    // User management
    getUsers() {
        return this.getItem(this.storageKeys.users) || [];
    }
    
    saveUsers(users) {
        return this.setItem(this.storageKeys.users, users);
    }
    
    getUserByCredentials(username, password, role) {
        const users = this.getUsers();
        console.log('getUserByCredentials called with:', { username, password, role });
        console.log('Available users:', users.map(u => ({
            username: u.username,
            role: u.role,
            active: u.active,
            passwordMatch: u.password === password
        })));

        const user = users.find(u => {
            const usernameMatch = u.username === username;
            const passwordMatch = u.password === password;
            const roleMatch = u.role === role;
            const isActive = u.active !== false;

            console.log(`Checking user ${u.username}:`, {
                usernameMatch,
                passwordMatch,
                roleMatch,
                isActive,
                overall: usernameMatch && passwordMatch && roleMatch && isActive
            });

            return usernameMatch && passwordMatch && roleMatch && isActive;
        });

        console.log('Found user:', user);
        return user;
    }
    
    getUserById(userId) {
        const users = this.getUsers();
        return users.find(u => u.id === userId);
    }
    
    // Settings management
    getSettings() {
        return this.getItem(this.storageKeys.settings);
    }
    
    saveSettings(settings) {
        return this.setItem(this.storageKeys.settings, settings);
    }
    
    updateSetting(key, value) {
        const settings = this.getSettings();
        if (settings) {
            settings[key] = value;
            return this.saveSettings(settings);
        }
        return false;
    }
    
    // Backup and restore
    createBackup() {
        const backupData = {
            customers: this.getCustomers(),
            suppliers: this.getSuppliers(),
            telesales: this.getTelesales(),
            tasks: this.getTasks(),
            users: this.getUsers(),
            settings: this.getSettings(),
            timestamp: new Date().toISOString(),
            version: '1.0.0'
        };
        
        this.setItem(this.storageKeys.backup, backupData);
        return backupData;
    }
    
    restoreBackup(backupData) {
        try {
            if (backupData.customers) this.saveCustomers(backupData.customers);
            if (backupData.suppliers) this.saveSuppliers(backupData.suppliers);
            if (backupData.telesales) this.saveTelesales(backupData.telesales);
            if (backupData.tasks) this.saveTasks(backupData.tasks);
            if (backupData.users) this.saveUsers(backupData.users);
            if (backupData.settings) this.saveSettings(backupData.settings);
            return true;
        } catch (error) {
            console.error('Error restoring backup:', error);
            return false;
        }
    }
    
    exportData() {
        return this.createBackup();
    }
    
    clearAllData() {
        Object.values(this.storageKeys).forEach(key => {
            this.removeItem(key);
        });
        this.initializeDefaultData();
    }
    
    // Utility methods
    generateId() {
        return Date.now() + Math.random().toString(36).substr(2, 9);
    }
    
    getStorageSize() {
        let total = 0;
        for (let key in localStorage) {
            if (localStorage.hasOwnProperty(key)) {
                total += localStorage[key].length + key.length;
            }
        }
        return total;
    }
    
    getStorageSizeFormatted() {
        const size = this.getStorageSize();
        const units = ['B', 'KB', 'MB', 'GB'];
        let unitIndex = 0;
        let formattedSize = size;
        
        while (formattedSize >= 1024 && unitIndex < units.length - 1) {
            formattedSize /= 1024;
            unitIndex++;
        }
        
        return `${formattedSize.toFixed(2)} ${units[unitIndex]}`;
    }
    
    // Search functionality
    searchAll(query) {
        const results = {
            customers: [],
            suppliers: [],
            telesales: [],
            tasks: []
        };
        
        if (!query || query.trim() === '') {
            return results;
        }
        
        const searchTerm = query.toLowerCase().trim();
        
        // Search customers
        const customers = this.getCustomers();
        results.customers = customers.filter(customer => 
            Object.values(customer).some(value => 
                value && value.toString().toLowerCase().includes(searchTerm)
            )
        );
        
        // Search suppliers
        const suppliers = this.getSuppliers();
        results.suppliers = suppliers.filter(supplier => 
            Object.values(supplier).some(value => 
                value && value.toString().toLowerCase().includes(searchTerm)
            )
        );
        
        // Search telesales
        const telesales = this.getTelesales();
        results.telesales = telesales.filter(employee => 
            Object.values(employee).some(value => 
                value && value.toString().toLowerCase().includes(searchTerm)
            )
        );
        
        // Search tasks
        const tasks = this.getTasks();
        results.tasks = tasks.filter(task => 
            Object.values(task).some(value => 
                value && value.toString().toLowerCase().includes(searchTerm)
            )
        );
        
        return results;
    }
    
    // Statistics
    getStatistics() {
        return {
            customers: {
                total: this.getCustomers().length,
                active: this.getCustomers().filter(c => c.status === 'active').length,
                inactive: this.getCustomers().filter(c => c.status === 'inactive').length,
                prospects: this.getCustomers().filter(c => c.status === 'prospect').length
            },
            suppliers: {
                total: this.getSuppliers().length
            },
            telesales: {
                total: this.getTelesales().length,
                active: this.getTelesales().filter(e => e.active !== false).length
            },
            tasks: {
                total: this.getTasks().length,
                pending: this.getTasks().filter(t => t.status === 'pending').length,
                inProgress: this.getTasks().filter(t => t.status === 'in_progress').length,
                completed: this.getTasks().filter(t => t.status === 'completed').length
            }
        };
    }
}

// Global storage manager instance
let storageManager;

// Initialize storage system
function initializeStorage() {
    storageManager = new StorageManager();
    return storageManager;
}

// Helper functions for global access
function getStorageManager() {
    if (!storageManager) {
        storageManager = new StorageManager();
    }
    return storageManager;
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { StorageManager, initializeStorage, getStorageManager };
}
