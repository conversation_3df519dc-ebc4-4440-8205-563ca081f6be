<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول مبسط - CRM</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 400px;
        }
        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        .logo i {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        h1 {
            text-align: center;
            color: #333;
            margin: 0 0 2rem 0;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #555;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            box-sizing: border-box;
        }
        input:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            margin-top: 1rem;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .quick-login {
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .quick-btn {
            width: 100%;
            background: #28a745;
            color: white;
            border: none;
            padding: 0.5rem;
            border-radius: 3px;
            margin: 0.25rem 0;
            cursor: pointer;
        }
        .message {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
            text-align: center;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <i class="fas fa-users-cog"></i>
            <h1>نظام إدارة علاقات العملاء</h1>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" required>
            </div>
            
            <div class="form-group">
                <label for="role">الدور:</label>
                <select id="role" required>
                    <option value="">اختر الدور</option>
                    <option value="manager">مدير</option>
                    <option value="supervisor">مشرف</option>
                    <option value="telesales">مبيعات هاتفية</option>
                </select>
            </div>
            
            <button type="submit" class="btn">تسجيل الدخول</button>
        </form>
        
        <div id="message"></div>
        
        <div class="quick-login">
            <p><strong>دخول سريع:</strong></p>
            <button class="quick-btn" onclick="quickLogin('admin', 'admin123', 'manager')">دخول كمدير</button>
            <button class="quick-btn" onclick="quickLogin('supervisor', 'super123', 'supervisor')">دخول كمشرف</button>
            <button class="quick-btn" onclick="quickLogin('sales', 'sales123', 'telesales')">دخول كموظف مبيعات</button>
        </div>
    </div>

    <script>
        // Simple user database
        const users = {
            'admin-admin123-manager': {
                username: 'admin',
                password: 'admin123',
                role: 'manager',
                name: 'مدير النظام'
            },
            'supervisor-super123-supervisor': {
                username: 'supervisor',
                password: 'super123',
                role: 'supervisor',
                name: 'مشرف المبيعات'
            },
            'sales-sales123-telesales': {
                username: 'sales',
                password: 'sales123',
                role: 'telesales',
                name: 'موظف المبيعات'
            }
        };

        function showMessage(text, type = 'error') {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="message ${type}">${text}</div>`;
        }

        function quickLogin(username, password, role) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            document.getElementById('role').value = role;
            
            // Trigger login
            login(username, password, role);
        }

        function login(username, password, role) {
            const key = `${username}-${password}-${role}`;
            const user = users[key];
            
            if (user) {
                // Save user session
                localStorage.setItem('currentUser', JSON.stringify(user));
                localStorage.setItem('isLoggedIn', 'true');
                
                showMessage(`مرحباً ${user.name}! جاري التحويل...`, 'success');
                
                // Redirect to dashboard
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);
                
                return true;
            } else {
                showMessage('بيانات الدخول غير صحيحة. يرجى التحقق من اسم المستخدم وكلمة المرور والدور.', 'error');
                return false;
            }
        }

        // Handle form submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const role = document.getElementById('role').value;
            
            if (!username || !password || !role) {
                showMessage('يرجى ملء جميع الحقول المطلوبة.', 'error');
                return;
            }
            
            login(username, password, role);
        });

        // Check if already logged in
        if (localStorage.getItem('isLoggedIn') === 'true') {
            const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
            if (currentUser.username) {
                showMessage(`مرحباً ${currentUser.name}! أنت مسجل دخول بالفعل. جاري التحويل...`, 'success');
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);
            }
        }
    </script>
</body>
</html>
