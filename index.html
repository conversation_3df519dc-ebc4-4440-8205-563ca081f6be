<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة علاقات العملاء - CRM System</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-users-cog"></i>
                    <h1 data-key="app_title">نظام إدارة علاقات العملاء</h1>
                </div>
                <div class="language-toggle">
                    <button id="languageToggle" class="btn-language">
                        <i class="fas fa-globe"></i>
                        <span data-key="english">English</span>
                    </button>
                </div>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username" data-key="username">اسم المستخدم</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="username" name="username" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password" data-key="password">كلمة المرور</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" required>
                        <button type="button" class="toggle-password" onclick="togglePassword()">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="role" data-key="role">الدور</label>
                    <div class="input-group">
                        <i class="fas fa-user-tag"></i>
                        <select id="role" name="role" required>
                            <option value="" data-key="select_role">اختر الدور</option>
                            <option value="manager" data-key="manager">مدير</option>
                            <option value="supervisor" data-key="supervisor">مشرف</option>
                            <option value="telesales" data-key="telesales">مبيعات هاتفية</option>
                        </select>
                    </div>
                </div>
                
                <button type="submit" class="btn-login" data-key="login">تسجيل الدخول</button>
                
                <div class="login-help">
                    <p data-key="demo_accounts">حسابات تجريبية:</p>
                    <small>
                        <strong data-key="manager">مدير:</strong> admin/admin123<br>
                        <strong data-key="supervisor">مشرف:</strong> supervisor/super123<br>
                        <strong data-key="telesales">مبيعات:</strong> sales/sales123
                    </small>
                </div>
            </form>
            
            <div id="loginError" class="error-message" style="display: none;"></div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p data-key="loading">جاري التحميل...</p>
        </div>
    </div>

    <script src="js/language.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize language system
            initializeLanguage();
            
            // Initialize authentication
            initializeAuth();
            
            // Check if user is already logged in
            const currentUser = getCurrentUser();
            if (currentUser) {
                window.location.href = 'dashboard.html';
            }
        });

        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.querySelector('.toggle-password i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        // Handle login form submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const role = document.getElementById('role').value;
            
            showLoading();
            
            // Simulate login delay for better UX
            setTimeout(() => {
                const loginResult = login(username, password, role);
                
                hideLoading();
                
                if (loginResult.success) {
                    window.location.href = 'dashboard.html';
                } else {
                    showError(loginResult.message);
                }
            }, 1000);
        });

        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        function showError(message) {
            const errorDiv = document.getElementById('loginError');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            
            // Hide error after 5 seconds
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html>
