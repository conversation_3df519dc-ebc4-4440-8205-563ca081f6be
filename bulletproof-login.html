<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM Login - Bulletproof Authentication</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-container {
            background: white;
            padding: 2.5rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 450px;
            position: relative;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .logo i {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
            display: block;
        }
        
        .logo h1 {
            color: #333;
            font-size: 1.5rem;
            margin: 0;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #555;
            font-weight: 600;
            font-size: 1rem;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .quick-access {
            margin-top: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }
        
        .quick-access h3 {
            color: #495057;
            margin-bottom: 1rem;
            font-size: 1rem;
            text-align: center;
        }
        
        .quick-btn {
            width: 100%;
            background: #28a745;
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 6px;
            margin: 0.5rem 0;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .quick-btn:hover {
            background: #218838;
            transform: translateY(-1px);
        }
        
        .quick-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .message {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .credentials-display {
            background: #e9ecef;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            font-size: 0.9rem;
        }
        
        .credentials-display h4 {
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .credential-item {
            margin: 0.25rem 0;
            color: #6c757d;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            font-size: 0.85rem;
            display: none;
        }
        
        @media (max-width: 480px) {
            .login-container {
                padding: 1.5rem;
                margin: 10px;
            }
            
            .logo h1 {
                font-size: 1.25rem;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <i class="fas fa-users-cog"></i>
            <h1>نظام إدارة علاقات العملاء</h1>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" required autocomplete="username">
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
            </div>
            
            <div class="form-group">
                <label for="role">الدور الوظيفي:</label>
                <select id="role" name="role" required>
                    <option value="">-- اختر الدور --</option>
                    <option value="manager">مدير النظام</option>
                    <option value="supervisor">مشرف المبيعات</option>
                    <option value="telesales">موظف المبيعات</option>
                </select>
            </div>
            
            <button type="submit" class="login-btn" id="loginButton">
                <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
            </button>
        </form>
        
        <div id="messageContainer"></div>
        
        <div class="quick-access">
            <h3><i class="fas fa-bolt"></i> دخول سريع للاختبار</h3>
            <button class="quick-btn" onclick="quickLogin('admin', 'admin123', 'manager')">
                <i class="fas fa-crown"></i> دخول كمدير النظام
            </button>
            <button class="quick-btn" onclick="quickLogin('supervisor', 'super123', 'supervisor')">
                <i class="fas fa-user-tie"></i> دخول كمشرف المبيعات
            </button>
            <button class="quick-btn" onclick="quickLogin('sales', 'sales123', 'telesales')">
                <i class="fas fa-headset"></i> دخول كموظف المبيعات
            </button>
        </div>
        
        <div class="credentials-display">
            <h4><i class="fas fa-key"></i> بيانات الدخول الصحيحة:</h4>
            <div class="credential-item"><strong>مدير:</strong> admin / admin123 / manager</div>
            <div class="credential-item"><strong>مشرف:</strong> supervisor / super123 / supervisor</div>
            <div class="credential-item"><strong>مبيعات:</strong> sales / sales123 / telesales</div>
        </div>
        
        <div id="debugInfo" class="debug-info">
            <h4>معلومات التشخيص:</h4>
            <div id="debugContent"></div>
        </div>
    </div>

    <script>
        // BULLETPROOF USER DATABASE - Simple Array-Based Lookup
        const VALID_USERS = [
            {
                username: 'admin',
                password: 'admin123',
                role: 'manager',
                name: 'مدير النظام',
                permissions: ['all']
            },
            {
                username: 'supervisor',
                password: 'super123',
                role: 'supervisor',
                name: 'مشرف المبيعات',
                permissions: ['read', 'write', 'manage_team']
            },
            {
                username: 'sales',
                password: 'sales123',
                role: 'telesales',
                name: 'موظف المبيعات',
                permissions: ['read', 'write_own']
            }
        ];

        let isProcessing = false;

        // BULLETPROOF MESSAGE DISPLAY
        function showMessage(text, type = 'info', showSpinner = false) {
            const container = document.getElementById('messageContainer');
            const spinnerHtml = showSpinner ? '<span class="loading-spinner"></span>' : '';
            container.innerHTML = `<div class="message ${type}">${spinnerHtml}${text}</div>`;
            
            // Auto-hide success messages after 3 seconds
            if (type === 'success') {
                setTimeout(() => {
                    container.innerHTML = '';
                }, 3000);
            }
        }

        // BULLETPROOF AUTHENTICATION FUNCTION
        function authenticateUser(username, password, role) {
            // Debug logging
            console.log('Authentication attempt:', { username, password: '***', role });
            
            // Input validation
            if (!username || !password || !role) {
                return {
                    success: false,
                    message: 'جميع الحقول مطلوبة',
                    user: null
                };
            }

            // Trim inputs
            username = username.trim().toLowerCase();
            password = password.trim();
            role = role.trim().toLowerCase();

            // Find user with exact match
            const user = VALID_USERS.find(u => 
                u.username.toLowerCase() === username &&
                u.password === password &&
                u.role.toLowerCase() === role
            );

            if (user) {
                return {
                    success: true,
                    message: `مرحباً ${user.name}!`,
                    user: user
                };
            } else {
                // Debug: Show what we're looking for vs what we have
                const debugInfo = {
                    searchFor: { username, password: '***', role },
                    availableUsers: VALID_USERS.map(u => ({
                        username: u.username,
                        role: u.role,
                        passwordMatch: u.password === password
                    }))
                };
                console.log('Authentication failed. Debug info:', debugInfo);
                
                return {
                    success: false,
                    message: 'بيانات الدخول غير صحيحة. يرجى التحقق من اسم المستخدم وكلمة المرور والدور.',
                    user: null
                };
            }
        }

        // BULLETPROOF LOGIN FUNCTION
        async function performLogin(username, password, role) {
            if (isProcessing) return;
            
            try {
                isProcessing = true;
                setButtonsDisabled(true);
                showMessage('جاري التحقق من بيانات الدخول...', 'info', true);

                // Simulate network delay for better UX
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Authenticate
                const authResult = authenticateUser(username, password, role);

                if (authResult.success) {
                    // Save session
                    const sessionData = {
                        user: authResult.user,
                        loginTime: new Date().toISOString(),
                        sessionId: generateSessionId()
                    };

                    localStorage.setItem('crmSession', JSON.stringify(sessionData));
                    localStorage.setItem('isLoggedIn', 'true');

                    showMessage(`${authResult.message} جاري التحويل إلى لوحة التحكم...`, 'success');

                    // Redirect after short delay
                    setTimeout(() => {
                        window.location.href = 'simple-dashboard.html';
                    }, 2000);

                } else {
                    showMessage(authResult.message, 'error');
                    showDebugInfo(username, password, role);
                }

            } catch (error) {
                console.error('Login error:', error);
                showMessage('حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.', 'error');
            } finally {
                isProcessing = false;
                setButtonsDisabled(false);
            }
        }

        // QUICK LOGIN FUNCTION
        function quickLogin(username, password, role) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            document.getElementById('role').value = role;
            
            performLogin(username, password, role);
        }

        // UTILITY FUNCTIONS
        function setButtonsDisabled(disabled) {
            document.getElementById('loginButton').disabled = disabled;
            document.querySelectorAll('.quick-btn').forEach(btn => btn.disabled = disabled);
        }

        function generateSessionId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2, 9);
        }

        function showDebugInfo(username, password, role) {
            const debugContainer = document.getElementById('debugInfo');
            const debugContent = document.getElementById('debugContent');
            
            debugContent.innerHTML = `
                <div><strong>المدخلات:</strong></div>
                <div>اسم المستخدم: "${username}"</div>
                <div>كلمة المرور: "${password}"</div>
                <div>الدور: "${role}"</div>
                <div><strong>المتوقع:</strong></div>
                <div>admin/admin123/manager أو supervisor/super123/supervisor أو sales/sales123/telesales</div>
            `;
            
            debugContainer.style.display = 'block';
        }

        // FORM SUBMISSION HANDLER
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const role = document.getElementById('role').value;
            
            performLogin(username, password, role);
        });

        // CHECK EXISTING SESSION
        function checkExistingSession() {
            try {
                const isLoggedIn = localStorage.getItem('isLoggedIn');
                const sessionData = localStorage.getItem('crmSession');
                
                if (isLoggedIn === 'true' && sessionData) {
                    const session = JSON.parse(sessionData);
                    if (session.user && session.user.name) {
                        showMessage(`مرحباً ${session.user.name}! أنت مسجل دخول بالفعل. جاري التحويل...`, 'success');
                        setTimeout(() => {
                            window.location.href = 'simple-dashboard.html';
                        }, 2000);
                        return;
                    }
                }
                
                // Clear invalid session
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('crmSession');
            } catch (error) {
                console.error('Session check error:', error);
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('crmSession');
            }
        }

        // INITIALIZE PAGE
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Bulletproof login system initialized');
            console.log('Available users:', VALID_USERS.map(u => `${u.username}/${u.role}`));
            checkExistingSession();
        });

        // GLOBAL ERROR HANDLER
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
            showMessage('حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.', 'error');
        });
    </script>
</body>
</html>
