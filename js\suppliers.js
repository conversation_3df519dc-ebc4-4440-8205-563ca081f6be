// Supplier Management Module for CRM
class SupplierManager {
    constructor() {
        this.currentSuppliers = [];
        this.filteredSuppliers = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortField = 'supplierName';
        this.sortDirection = 'asc';
        this.filters = {};
        
        this.init();
    }
    
    init() {
        this.loadSuppliers();
        this.bindEvents();
    }
    
    bindEvents() {
        // Listen for module navigation
        document.addEventListener('navigateToModule', (e) => {
            if (e.detail.module === 'suppliers') {
                this.handleNavigation(e.detail);
            }
        });
    }
    
    handleNavigation(detail) {
        if (detail.action === 'view' && detail.id) {
            this.viewSupplier(detail.id);
        } else {
            this.showSuppliersModule();
        }
    }
    
    loadSuppliers() {
        const storage = getStorageManager();
        this.currentSuppliers = storage.getSuppliers();
        this.filteredSuppliers = [...this.currentSuppliers];
        this.applyFiltersAndSort();
    }
    
    showSuppliersModule() {
        const moduleContent = this.createSuppliersModule();
        const suppliersModule = document.getElementById('suppliersModule');
        
        if (suppliersModule) {
            suppliersModule.innerHTML = moduleContent;
            this.bindModuleEvents();
            this.renderSuppliersTable();
            this.updateStatistics();
        }
    }
    
    createSuppliersModule() {
        return `
            <div class="module-header">
                <h2 data-key="supplier_management">${getTranslation('supplier_management')}</h2>
                <div class="module-actions">
                    <button class="btn btn-primary" id="addSupplierBtn" data-permission="suppliers:create">
                        <i class="fas fa-plus"></i>
                        <span data-key="add_supplier">${getTranslation('add_supplier')}</span>
                    </button>
                    <button class="btn btn-secondary" id="exportSuppliersBtn" data-permission="suppliers:export">
                        <i class="fas fa-download"></i>
                        <span data-key="export">${getTranslation('export')}</span>
                    </button>
                </div>
            </div>
            
            <div class="suppliers-content">
                <!-- Statistics Cards -->
                <div class="stats-row">
                    <div class="stat-card">
                        <div class="stat-icon suppliers">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalSuppliersCount">0</h3>
                            <p data-key="total_suppliers">${getTranslation('total_suppliers')}</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon services">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalServicesCount">0</h3>
                            <p data-key="service_types">${getTranslation('service_types')}</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon cities">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="supplierCitiesCount">0</h3>
                            <p data-key="cities_covered">${getTranslation('cities_covered')}</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon recent">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="recentSuppliersCount">0</h3>
                            <p data-key="recent_suppliers">${getTranslation('recent_suppliers')}</p>
                        </div>
                    </div>
                </div>
                
                <!-- Filters and Search -->
                <div class="filters-section">
                    <div class="filters-row">
                        <div class="filter-group">
                            <label data-key="search">${getTranslation('search')}</label>
                            <div class="search-input">
                                <i class="fas fa-search"></i>
                                <input type="text" id="supplierSearch" placeholder="${getTranslation('search_suppliers')}" data-key-placeholder="search_suppliers">
                            </div>
                        </div>
                        
                        <div class="filter-group">
                            <label data-key="service_type">${getTranslation('service_type')}</label>
                            <select id="serviceTypeFilter">
                                <option value="">${getTranslation('all_services')}</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label data-key="city">${getTranslation('city')}</label>
                            <select id="cityFilter">
                                <option value="">${getTranslation('all_cities')}</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label data-key="contact_method">${getTranslation('contact_method')}</label>
                            <select id="contactMethodFilter">
                                <option value="">${getTranslation('all_methods')}</option>
                                <option value="phone">${getTranslation('phone')}</option>
                                <option value="email">${getTranslation('email')}</option>
                                <option value="both">${getTranslation('both')}</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <button class="btn btn-secondary" id="clearFiltersBtn">
                                <i class="fas fa-times"></i>
                                <span data-key="clear_filters">${getTranslation('clear_filters')}</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Suppliers Table -->
                <div class="table-container">
                    <div class="table-header">
                        <div class="table-info">
                            <span id="tableInfo">${getTranslation('showing')} 0 ${getTranslation('of')} 0 ${getTranslation('entries')}</span>
                        </div>
                        <div class="table-controls">
                            <div class="page-size-selector">
                                <label data-key="show">${getTranslation('show')}</label>
                                <select id="pageSizeSelect">
                                    <option value="10">10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                                <span data-key="entries">${getTranslation('entries')}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-wrapper">
                        <table class="data-table" id="suppliersTable">
                            <thead>
                                <tr>
                                    <th data-sort="supplierName" class="sortable">
                                        <span data-key="supplier_name">${getTranslation('supplier_name')}</span>
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th data-sort="serviceType" class="sortable">
                                        <span data-key="service_type">${getTranslation('service_type')}</span>
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th data-sort="contactMethod" class="sortable">
                                        <span data-key="contact_method">${getTranslation('contact_method')}</span>
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th data-sort="phone" class="sortable">
                                        <span data-key="phone">${getTranslation('phone')}</span>
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th data-sort="email" class="sortable">
                                        <span data-key="email">${getTranslation('email')}</span>
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th data-sort="city" class="sortable">
                                        <span data-key="city">${getTranslation('city')}</span>
                                        <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="actions-column">
                                        <span data-key="actions">${getTranslation('actions')}</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="suppliersTableBody">
                                <!-- Table rows will be inserted here -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="table-footer">
                        <div class="pagination" id="suppliersPagination">
                            <!-- Pagination will be inserted here -->
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    bindModuleEvents() {
        // Add supplier button
        const addBtn = document.getElementById('addSupplierBtn');
        if (addBtn && hasPermission('suppliers', 'create')) {
            addBtn.addEventListener('click', () => this.showAddSupplierModal());
        }
        
        // Export button
        const exportBtn = document.getElementById('exportSuppliersBtn');
        if (exportBtn && hasPermission('suppliers', 'export')) {
            exportBtn.addEventListener('click', () => this.exportSuppliers());
        }
        
        // Search input
        const searchInput = document.getElementById('supplierSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filters.search = e.target.value;
                this.applyFiltersAndSort();
                this.renderSuppliersTable();
            });
        }
        
        // Filter selects
        ['serviceTypeFilter', 'cityFilter', 'contactMethodFilter'].forEach(filterId => {
            const filterElement = document.getElementById(filterId);
            if (filterElement) {
                filterElement.addEventListener('change', (e) => {
                    const filterType = filterId.replace('Filter', '');
                    this.filters[filterType] = e.target.value;
                    this.applyFiltersAndSort();
                    this.renderSuppliersTable();
                });
            }
        });
        
        // Clear filters button
        const clearFiltersBtn = document.getElementById('clearFiltersBtn');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => this.clearFilters());
        }
        
        // Page size selector
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        if (pageSizeSelect) {
            pageSizeSelect.value = this.pageSize;
            pageSizeSelect.addEventListener('change', (e) => {
                this.pageSize = parseInt(e.target.value);
                this.currentPage = 1;
                this.renderSuppliersTable();
            });
        }
        
        // Table sorting
        const sortableHeaders = document.querySelectorAll('#suppliersTable th.sortable');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', () => {
                const field = header.getAttribute('data-sort');
                this.handleSort(field);
            });
        });
    }
    
    applyFiltersAndSort() {
        let filtered = [...this.currentSuppliers];
        
        // Apply search filter
        if (this.filters.search) {
            const searchTerm = this.filters.search.toLowerCase();
            filtered = filtered.filter(supplier => 
                Object.values(supplier).some(value => 
                    value && value.toString().toLowerCase().includes(searchTerm)
                )
            );
        }
        
        // Apply service type filter
        if (this.filters.serviceType) {
            filtered = filtered.filter(supplier => supplier.serviceType === this.filters.serviceType);
        }
        
        // Apply city filter
        if (this.filters.city) {
            filtered = filtered.filter(supplier => supplier.city === this.filters.city);
        }
        
        // Apply contact method filter
        if (this.filters.contactMethod) {
            filtered = filtered.filter(supplier => supplier.contactMethod === this.filters.contactMethod);
        }
        
        // Apply sorting
        filtered.sort((a, b) => {
            const aValue = a[this.sortField] || '';
            const bValue = b[this.sortField] || '';
            
            if (this.sortDirection === 'asc') {
                return aValue.toString().localeCompare(bValue.toString());
            } else {
                return bValue.toString().localeCompare(aValue.toString());
            }
        });
        
        this.filteredSuppliers = filtered;
        this.currentPage = 1; // Reset to first page when filters change
    }
    
    handleSort(field) {
        if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortDirection = 'asc';
        }
        
        this.applyFiltersAndSort();
        this.renderSuppliersTable();
        this.updateSortIcons();
    }
    
    updateSortIcons() {
        // Reset all sort icons
        document.querySelectorAll('#suppliersTable th.sortable i').forEach(icon => {
            icon.className = 'fas fa-sort';
        });
        
        // Set active sort icon
        const activeHeader = document.querySelector(`#suppliersTable th[data-sort="${this.sortField}"] i`);
        if (activeHeader) {
            activeHeader.className = `fas fa-sort-${this.sortDirection === 'asc' ? 'up' : 'down'}`;
        }
    }
    
    renderSuppliersTable() {
        const tbody = document.getElementById('suppliersTableBody');
        if (!tbody) return;
        
        // Calculate pagination
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageSuppliers = this.filteredSuppliers.slice(startIndex, endIndex);
        
        // Render table rows
        tbody.innerHTML = pageSuppliers.map(supplier => this.createSupplierRow(supplier)).join('');
        
        // Update table info
        this.updateTableInfo();
        
        // Render pagination
        this.renderPagination();
        
        // Bind row events
        this.bindRowEvents();
        
        // Update filter options
        this.updateFilterOptions();
    }
    
    createSupplierRow(supplier) {
        return `
            <tr data-supplier-id="${supplier.id}">
                <td>
                    <div class="supplier-info">
                        <div class="supplier-name">${this.escapeHtml(supplier.supplierName || '')}</div>
                        ${supplier.notes ? `<div class="supplier-notes">${this.escapeHtml(supplier.notes.substring(0, 50))}${supplier.notes.length > 50 ? '...' : ''}</div>` : ''}
                    </div>
                </td>
                <td>
                    <span class="service-type-badge">${this.escapeHtml(supplier.serviceType || '')}</span>
                </td>
                <td>
                    <span class="contact-method-badge">${this.escapeHtml(supplier.contactMethod || '')}</span>
                </td>
                <td>
                    <a href="tel:${supplier.phone || ''}" class="phone-link">
                        ${this.escapeHtml(supplier.phone || '')}
                    </a>
                </td>
                <td>
                    <a href="mailto:${supplier.email || ''}" class="email-link">
                        ${this.escapeHtml(supplier.email || '')}
                    </a>
                </td>
                <td>${this.escapeHtml(supplier.city || '')}</td>
                <td class="actions-cell">
                    <div class="action-buttons">
                        <button class="btn-action btn-view" onclick="supplierManager.viewSupplier('${supplier.id}')" title="${getTranslation('view')}">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${hasPermission('suppliers', 'update') ? `
                            <button class="btn-action btn-edit" onclick="supplierManager.editSupplier('${supplier.id}')" title="${getTranslation('edit')}">
                                <i class="fas fa-edit"></i>
                            </button>
                        ` : ''}
                        ${hasPermission('suppliers', 'delete') ? `
                            <button class="btn-action btn-delete" onclick="supplierManager.deleteSupplier('${supplier.id}')" title="${getTranslation('delete')}">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    }
    
    bindRowEvents() {
        // Double-click to view supplier
        document.querySelectorAll('#suppliersTableBody tr').forEach(row => {
            row.addEventListener('dblclick', () => {
                const supplierId = row.getAttribute('data-supplier-id');
                this.viewSupplier(supplierId);
            });
        });
    }
    
    updateTableInfo() {
        const tableInfo = document.getElementById('tableInfo');
        if (tableInfo) {
            const startIndex = (this.currentPage - 1) * this.pageSize + 1;
            const endIndex = Math.min(this.currentPage * this.pageSize, this.filteredSuppliers.length);
            const total = this.filteredSuppliers.length;
            
            tableInfo.textContent = `${getTranslation('showing')} ${startIndex}-${endIndex} ${getTranslation('of')} ${total} ${getTranslation('entries')}`;
        }
    }
    
    renderPagination() {
        const pagination = document.getElementById('suppliersPagination');
        if (!pagination) return;
        
        const totalPages = Math.ceil(this.filteredSuppliers.length / this.pageSize);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        let html = '';
        
        // Previous button
        html += `
            <button class="pagination-btn ${this.currentPage === 1 ? 'disabled' : ''}" 
                    onclick="supplierManager.goToPage(${this.currentPage - 1})" 
                    ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
                <span data-key="previous">${getTranslation('previous')}</span>
            </button>
        `;
        
        // Page numbers
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);
        
        if (startPage > 1) {
            html += `<button class="pagination-btn" onclick="supplierManager.goToPage(1)">1</button>`;
            if (startPage > 2) {
                html += `<span class="pagination-ellipsis">...</span>`;
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                        onclick="supplierManager.goToPage(${i})">
                    ${i}
                </button>
            `;
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += `<span class="pagination-ellipsis">...</span>`;
            }
            html += `<button class="pagination-btn" onclick="supplierManager.goToPage(${totalPages})">${totalPages}</button>`;
        }
        
        // Next button
        html += `
            <button class="pagination-btn ${this.currentPage === totalPages ? 'disabled' : ''}" 
                    onclick="supplierManager.goToPage(${this.currentPage + 1})" 
                    ${this.currentPage === totalPages ? 'disabled' : ''}>
                <span data-key="next">${getTranslation('next')}</span>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        pagination.innerHTML = html;
    }
    
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredSuppliers.length / this.pageSize);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderSuppliersTable();
        }
    }
    
    updateStatistics() {
        const serviceTypes = [...new Set(this.currentSuppliers.map(s => s.serviceType).filter(Boolean))];
        const cities = [...new Set(this.currentSuppliers.map(s => s.city).filter(Boolean))];
        const recentSuppliers = this.currentSuppliers.filter(s => {
            const createdDate = new Date(s.createdAt);
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            return createdDate > weekAgo;
        });
        
        document.getElementById('totalSuppliersCount').textContent = this.currentSuppliers.length;
        document.getElementById('totalServicesCount').textContent = serviceTypes.length;
        document.getElementById('supplierCitiesCount').textContent = cities.length;
        document.getElementById('recentSuppliersCount').textContent = recentSuppliers.length;
    }
    
    updateFilterOptions() {
        // Update service type filter options
        const serviceTypes = [...new Set(this.currentSuppliers.map(s => s.serviceType).filter(Boolean))].sort();
        const serviceTypeFilter = document.getElementById('serviceTypeFilter');
        if (serviceTypeFilter) {
            const currentValue = serviceTypeFilter.value;
            serviceTypeFilter.innerHTML = `<option value="">${getTranslation('all_services')}</option>` +
                serviceTypes.map(type => `<option value="${type}">${type}</option>`).join('');
            serviceTypeFilter.value = currentValue;
        }
        
        // Update city filter options
        const cities = [...new Set(this.currentSuppliers.map(s => s.city).filter(Boolean))].sort();
        const cityFilter = document.getElementById('cityFilter');
        if (cityFilter) {
            const currentValue = cityFilter.value;
            cityFilter.innerHTML = `<option value="">${getTranslation('all_cities')}</option>` +
                cities.map(city => `<option value="${city}">${city}</option>`).join('');
            cityFilter.value = currentValue;
        }
    }
    
    clearFilters() {
        this.filters = {};
        this.currentPage = 1;
        
        // Reset filter inputs
        document.getElementById('supplierSearch').value = '';
        document.getElementById('serviceTypeFilter').value = '';
        document.getElementById('cityFilter').value = '';
        document.getElementById('contactMethodFilter').value = '';
        
        this.applyFiltersAndSort();
        this.renderSuppliersTable();
    }
    
    // Supplier CRUD operations
    showAddSupplierModal() {
        console.log('Show add supplier modal');
    }
    
    viewSupplier(supplierId) {
        console.log('View supplier:', supplierId);
    }
    
    editSupplier(supplierId) {
        console.log('Edit supplier:', supplierId);
    }
    
    deleteSupplier(supplierId) {
        if (confirm(getTranslation('confirm_delete'))) {
            const storage = getStorageManager();
            if (storage.deleteSupplier(supplierId)) {
                this.loadSuppliers();
                this.renderSuppliersTable();
                this.updateStatistics();
                
                // Show success message
                if (window.exportManager) {
                    exportManager.showNotification(getTranslation('supplier_deleted'), 'success');
                }
            }
        }
    }
    
    exportSuppliers() {
        if (window.exportManager) {
            exportManager.exportSuppliersToExcel();
        }
    }
    
    // Utility methods
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Global supplier manager instance
let supplierManager;

// Initialize supplier management
function initializeSuppliers() {
    supplierManager = new SupplierManager();
    return supplierManager;
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SupplierManager, initializeSuppliers };
}
