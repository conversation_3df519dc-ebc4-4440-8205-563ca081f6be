<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام CRM</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #f8f9fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.5rem;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .welcome-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .welcome-card h2 {
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .customers .stat-icon { color: #28a745; }
        .suppliers .stat-icon { color: #17a2b8; }
        .telesales .stat-icon { color: #ffc107; }
        .tasks .stat-icon { color: #dc3545; }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 1rem;
        }
        
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .module-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .module-card:hover {
            transform: translateY(-5px);
        }
        
        .module-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .module-icon {
            font-size: 1.5rem;
            color: #667eea;
        }
        
        .module-title {
            font-size: 1.25rem;
            font-weight: bold;
            color: #333;
        }
        
        .module-description {
            color: #666;
            margin-bottom: 1rem;
            line-height: 1.5;
        }
        
        .module-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
        }
        
        .module-btn:hover {
            opacity: 0.9;
        }
        
        .footer {
            text-align: center;
            padding: 2rem;
            color: #666;
            margin-top: 3rem;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }
            
            .container {
                padding: 0 0.5rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }
            
            .modules-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header class="header">
        <h1><i class="fas fa-users-cog"></i> نظام إدارة علاقات العملاء</h1>
        <div class="user-info">
            <span id="userName">مرحباً</span>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </button>
        </div>
    </header>

    <div class="container">
        <div class="welcome-card">
            <h2>مرحباً بك في نظام إدارة علاقات العملاء</h2>
            <p>يمكنك من هنا إدارة جميع عملياتك التجارية بكفاءة وسهولة</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card customers">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number">150</div>
                <div class="stat-label">إجمالي العملاء</div>
            </div>

            <div class="stat-card suppliers">
                <div class="stat-icon">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="stat-number">45</div>
                <div class="stat-label">إجمالي الموردين</div>
            </div>

            <div class="stat-card telesales">
                <div class="stat-icon">
                    <i class="fas fa-headset"></i>
                </div>
                <div class="stat-number">12</div>
                <div class="stat-label">فريق المبيعات</div>
            </div>

            <div class="stat-card tasks">
                <div class="stat-icon">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="stat-number">28</div>
                <div class="stat-label">المهام النشطة</div>
            </div>
        </div>

        <div class="modules-grid">
            <div class="module-card">
                <div class="module-header">
                    <div class="module-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="module-title">إدارة العملاء</div>
                </div>
                <div class="module-description">
                    إدارة شاملة لبيانات العملاء، إضافة عملاء جدد، تحديث المعلومات، ومتابعة حالة العملاء
                </div>
                <button class="module-btn" onclick="openModule('customers')">
                    <i class="fas fa-arrow-left"></i> دخول الوحدة
                </button>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <div class="module-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="module-title">إدارة الموردين</div>
                </div>
                <div class="module-description">
                    إدارة بيانات الموردين وخدماتهم، تصنيف الموردين، ومتابعة التعاملات التجارية
                </div>
                <button class="module-btn" onclick="openModule('suppliers')">
                    <i class="fas fa-arrow-left"></i> دخول الوحدة
                </button>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <div class="module-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="module-title">فريق المبيعات</div>
                </div>
                <div class="module-description">
                    إدارة فريق المبيعات الهاتفية، تخصيص المهام، ومتابعة الأداء والإنتاجية
                </div>
                <button class="module-btn" onclick="openModule('telesales')">
                    <i class="fas fa-arrow-left"></i> دخول الوحدة
                </button>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <div class="module-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="module-title">التقارير والإحصائيات</div>
                </div>
                <div class="module-description">
                    تقارير شاملة عن الأداء، إحصائيات المبيعات، وتحليلات البيانات التجارية
                </div>
                <button class="module-btn" onclick="openModule('reports')">
                    <i class="fas fa-arrow-left"></i> دخول الوحدة
                </button>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <div class="module-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="module-title">تصدير البيانات</div>
                </div>
                <div class="module-description">
                    تصدير البيانات بصيغ مختلفة (Excel, PDF)، إنشاء نسخ احتياطية، واستيراد البيانات
                </div>
                <button class="module-btn" onclick="exportData()">
                    <i class="fas fa-download"></i> تصدير البيانات
                </button>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <div class="module-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="module-title">إعدادات النظام</div>
                </div>
                <div class="module-description">
                    إعدادات عامة للنظام، إدارة المستخدمين، والتحكم في صلاحيات الوصول
                </div>
                <button class="module-btn" onclick="openModule('settings')">
                    <i class="fas fa-arrow-left"></i> دخول الوحدة
                </button>
            </div>
        </div>
    </div>

    <footer class="footer">
        <p>&copy; 2024 نظام إدارة علاقات العملاء. جميع الحقوق محفوظة.</p>
    </footer>

    <script>
        // Check if user is logged in
        function checkAuth() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
            
            if (isLoggedIn !== 'true' || !currentUser.username) {
                window.location.href = 'simple-login.html';
                return false;
            }
            
            // Update user name in header
            document.getElementById('userName').textContent = `مرحباً ${currentUser.name}`;
            return true;
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('currentUser');
                window.location.href = 'simple-login.html';
            }
        }

        function openModule(moduleName) {
            alert(`سيتم فتح وحدة ${getModuleName(moduleName)} قريباً!\n\nهذه نسخة تجريبية من النظام.`);
        }

        function exportData() {
            alert('سيتم تفعيل خاصية تصدير البيانات قريباً!\n\nيمكنك تصدير البيانات بصيغ Excel و PDF.');
        }

        function getModuleName(module) {
            const names = {
                'customers': 'إدارة العملاء',
                'suppliers': 'إدارة الموردين',
                'telesales': 'فريق المبيعات',
                'reports': 'التقارير والإحصائيات',
                'settings': 'إعدادات النظام'
            };
            return names[module] || module;
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
        });
    </script>
</body>
</html>
