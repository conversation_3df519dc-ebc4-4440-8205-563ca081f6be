// Authentication System for CRM
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.sessionKey = 'crm_session';
        this.sessionTimeout = 8 * 60 * 60 * 1000; // 8 hours in milliseconds
        
        this.permissions = {
            manager: {
                customers: ['create', 'read', 'update', 'delete', 'export'],
                suppliers: ['create', 'read', 'update', 'delete', 'export'],
                telesales: ['create', 'read', 'update', 'delete', 'export'],
                tasks: ['create', 'read', 'update', 'delete', 'assign'],
                reports: ['read', 'export'],
                settings: ['read', 'update'],
                users: ['create', 'read', 'update', 'delete'],
                backup: ['create', 'restore']
            },
            supervisor: {
                customers: ['create', 'read', 'update', 'export'],
                suppliers: ['create', 'read', 'update', 'export'],
                telesales: ['read', 'update', 'export'],
                tasks: ['create', 'read', 'update', 'assign'],
                reports: ['read', 'export'],
                settings: ['read'],
                users: ['read'],
                backup: ['create']
            },
            telesales: {
                customers: ['create', 'read', 'update'],
                suppliers: ['read'],
                telesales: ['read'],
                tasks: ['read', 'update'],
                reports: ['read'],
                settings: [],
                users: [],
                backup: []
            }
        };
        
        this.init();
    }
    
    init() {
        // Check for existing session
        this.loadSession();
        
        // Set up session timeout
        this.setupSessionTimeout();
    }
    
    // Login method
    login(username, password, role) {
        try {
            const storage = getStorageManager();
            const user = storage.getUserByCredentials(username, password, role);
            
            if (user) {
                // Create session
                const session = {
                    user: {
                        id: user.id,
                        username: user.username,
                        name: user.name,
                        email: user.email,
                        role: user.role
                    },
                    loginTime: new Date().toISOString(),
                    expiresAt: new Date(Date.now() + this.sessionTimeout).toISOString(),
                    sessionId: this.generateSessionId()
                };
                
                // Save session
                this.saveSession(session);
                this.currentUser = session.user;
                
                // Log login activity
                this.logActivity('login', 'User logged in successfully');
                
                return {
                    success: true,
                    user: this.currentUser,
                    message: getTranslation('login_success') || 'تم تسجيل الدخول بنجاح'
                };
            } else {
                return {
                    success: false,
                    message: getTranslation('invalid_credentials') || 'بيانات الدخول غير صحيحة'
                };
            }
        } catch (error) {
            console.error('Login error:', error);
            return {
                success: false,
                message: getTranslation('login_error') || 'خطأ في تسجيل الدخول'
            };
        }
    }
    
    // Logout method
    logout() {
        try {
            // Log logout activity
            if (this.currentUser) {
                this.logActivity('logout', 'User logged out');
            }
            
            // Clear session
            this.clearSession();
            this.currentUser = null;
            
            return {
                success: true,
                message: getTranslation('logout_success') || 'تم تسجيل الخروج بنجاح'
            };
        } catch (error) {
            console.error('Logout error:', error);
            return {
                success: false,
                message: getTranslation('logout_error') || 'خطأ في تسجيل الخروج'
            };
        }
    }
    
    // Check if user is authenticated
    isAuthenticated() {
        if (!this.currentUser) {
            return false;
        }
        
        // Check session expiry
        const session = this.getSession();
        if (!session || new Date() > new Date(session.expiresAt)) {
            this.logout();
            return false;
        }
        
        return true;
    }
    
    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }
    
    // Check user permission
    hasPermission(module, action) {
        if (!this.currentUser) {
            return false;
        }
        
        const userRole = this.currentUser.role;
        const modulePermissions = this.permissions[userRole]?.[module];
        
        if (!modulePermissions) {
            return false;
        }
        
        return modulePermissions.includes(action);
    }
    
    // Get user permissions for a module
    getModulePermissions(module) {
        if (!this.currentUser) {
            return [];
        }
        
        const userRole = this.currentUser.role;
        return this.permissions[userRole]?.[module] || [];
    }
    
    // Get all user permissions
    getAllPermissions() {
        if (!this.currentUser) {
            return {};
        }
        
        const userRole = this.currentUser.role;
        return this.permissions[userRole] || {};
    }
    
    // Check if user can access module
    canAccessModule(module) {
        const modulePermissions = this.getModulePermissions(module);
        return modulePermissions.length > 0;
    }
    
    // Session management
    saveSession(session) {
        try {
            localStorage.setItem(this.sessionKey, JSON.stringify(session));
            return true;
        } catch (error) {
            console.error('Error saving session:', error);
            return false;
        }
    }
    
    getSession() {
        try {
            const sessionData = localStorage.getItem(this.sessionKey);
            return sessionData ? JSON.parse(sessionData) : null;
        } catch (error) {
            console.error('Error reading session:', error);
            return null;
        }
    }
    
    loadSession() {
        const session = this.getSession();
        if (session && new Date() <= new Date(session.expiresAt)) {
            this.currentUser = session.user;
            return true;
        } else {
            this.clearSession();
            return false;
        }
    }
    
    clearSession() {
        try {
            localStorage.removeItem(this.sessionKey);
            return true;
        } catch (error) {
            console.error('Error clearing session:', error);
            return false;
        }
    }
    
    // Extend session
    extendSession() {
        const session = this.getSession();
        if (session) {
            session.expiresAt = new Date(Date.now() + this.sessionTimeout).toISOString();
            this.saveSession(session);
            return true;
        }
        return false;
    }
    
    // Session timeout management
    setupSessionTimeout() {
        // Extend session on user activity
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        let timeoutId;
        
        const resetTimeout = () => {
            clearTimeout(timeoutId);
            
            if (this.isAuthenticated()) {
                this.extendSession();
                
                // Set warning timeout (30 minutes before expiry)
                timeoutId = setTimeout(() => {
                    this.showSessionWarning();
                }, this.sessionTimeout - (30 * 60 * 1000));
            }
        };
        
        events.forEach(event => {
            document.addEventListener(event, resetTimeout, true);
        });
        
        // Initial timeout setup
        resetTimeout();
    }
    
    // Show session expiry warning
    showSessionWarning() {
        if (this.isAuthenticated()) {
            const extendSession = confirm(
                getTranslation('session_expiry_warning') || 
                'ستنتهي جلستك قريباً. هل تريد تمديدها؟'
            );
            
            if (extendSession) {
                this.extendSession();
            } else {
                this.logout();
                window.location.href = 'index.html';
            }
        }
    }
    
    // Activity logging
    logActivity(action, description) {
        try {
            const activities = JSON.parse(localStorage.getItem('crm_activities') || '[]');
            
            const activity = {
                id: this.generateSessionId(),
                userId: this.currentUser?.id,
                username: this.currentUser?.username,
                action: action,
                description: description,
                timestamp: new Date().toISOString(),
                ip: 'localhost', // In a real app, you'd get the actual IP
                userAgent: navigator.userAgent
            };
            
            activities.unshift(activity);
            
            // Keep only last 1000 activities
            if (activities.length > 1000) {
                activities.splice(1000);
            }
            
            localStorage.setItem('crm_activities', JSON.stringify(activities));
        } catch (error) {
            console.error('Error logging activity:', error);
        }
    }
    
    // Get user activities
    getUserActivities(userId = null, limit = 50) {
        try {
            const activities = JSON.parse(localStorage.getItem('crm_activities') || '[]');
            
            let filteredActivities = activities;
            if (userId) {
                filteredActivities = activities.filter(activity => activity.userId === userId);
            }
            
            return filteredActivities.slice(0, limit);
        } catch (error) {
            console.error('Error getting activities:', error);
            return [];
        }
    }
    
    // Utility methods
    generateSessionId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2, 9);
    }
    
    // Password validation (for future use)
    validatePassword(password) {
        const minLength = 6;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        
        return {
            isValid: password.length >= minLength,
            minLength: password.length >= minLength,
            hasUpperCase: hasUpperCase,
            hasLowerCase: hasLowerCase,
            hasNumbers: hasNumbers,
            score: [
                password.length >= minLength,
                hasUpperCase,
                hasLowerCase,
                hasNumbers
            ].filter(Boolean).length
        };
    }
    
    // Role-based UI helpers
    hideElementsForRole() {
        if (!this.currentUser) return;
        
        const role = this.currentUser.role;
        
        // Hide navigation items based on role
        if (role === 'telesales') {
            // Hide settings and reports for telesales
            const settingsNav = document.getElementById('settingsNav');
            const reportsNav = document.getElementById('reportsNav');
            
            if (settingsNav) settingsNav.style.display = 'none';
            if (reportsNav) reportsNav.style.display = 'none';
        }
        
        // Hide action buttons based on permissions
        document.querySelectorAll('[data-permission]').forEach(element => {
            const permission = element.getAttribute('data-permission');
            const [module, action] = permission.split(':');
            
            if (!this.hasPermission(module, action)) {
                element.style.display = 'none';
            }
        });
    }
    
    // Check if current user can perform action
    requirePermission(module, action) {
        if (!this.hasPermission(module, action)) {
            throw new Error(
                getTranslation('access_denied') || 
                'ليس لديك صلاحية للقيام بهذا الإجراء'
            );
        }
        return true;
    }
}

// Global auth manager instance
let authManager;

// Initialize authentication system
function initializeAuth() {
    authManager = new AuthManager();
    return authManager;
}

// Helper functions for global access
function login(username, password, role) {
    return authManager ? authManager.login(username, password, role) : { success: false, message: 'Auth not initialized' };
}

function logout() {
    return authManager ? authManager.logout() : { success: false, message: 'Auth not initialized' };
}

function isAuthenticated() {
    return authManager ? authManager.isAuthenticated() : false;
}

function getCurrentUser() {
    return authManager ? authManager.getCurrentUser() : null;
}

function hasPermission(module, action) {
    return authManager ? authManager.hasPermission(module, action) : false;
}

function requirePermission(module, action) {
    if (authManager) {
        return authManager.requirePermission(module, action);
    }
    throw new Error('Auth not initialized');
}

function canAccessModule(module) {
    return authManager ? authManager.canAccessModule(module) : false;
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { 
        AuthManager, 
        initializeAuth, 
        login, 
        logout, 
        isAuthenticated, 
        getCurrentUser, 
        hasPermission, 
        requirePermission,
        canAccessModule 
    };
}
