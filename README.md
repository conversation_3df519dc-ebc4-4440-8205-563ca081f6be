# نظام إدارة علاقات العملاء - CRM System

## نظرة عامة
نظام إدارة علاقات العملاء (CRM) هو تطبيق ويب شامل مصمم لإدارة العملاء والموردين وفريق المبيعات الهاتفية. يوفر النظام واجهة حديثة وسهلة الاستخدام مع دعم كامل للغة العربية والإنجليزية.

## المميزات الرئيسية

### 🧑‍💼 إدارة العملاء
- تخزين وعرض بيانات العملاء الكاملة
- البحث والفلترة المتقدمة
- تتبع حالة العملاء وتاريخ آخر تواصل
- إضافة الملاحظات والتعليقات

### 🚚 إدارة الموردين
- إدارة بيانات الموردين وخدماتهم
- تصنيف الموردين حسب نوع الخدمة والمدينة
- تتبع وسائل التواصل المختلفة

### 📞 إدارة فريق المبيعات
- إدارة بيانات الموظفين
- تخصيص وتتبع المهام اليومية
- مراقبة الأداء الأسبوعي
- تقارير الإنتاجية والأداء

### 🔍 البحث الشامل
- بحث فوري عبر جميع البيانات
- نتائج مصنفة ومنظمة
- تنقل سريع للعناصر

### 📊 التصدير والتقارير
- تصدير البيانات بصيغة Excel و PDF
- تقارير شاملة للإحصائيات
- نسخ احتياطية للبيانات

### 👥 نظام الصلاحيات
- **مدير**: صلاحية كاملة لجميع الوظائف
- **مشرف**: إشراف وتعديل البيانات
- **مبيعات**: عرض محدود وإدخال بيانات

### 🌐 دعم متعدد اللغات
- واجهة ثنائية اللغة (عربي/إنجليزي)
- دعم كامل للنصوص من اليمين لليسار (RTL)
- تبديل فوري بين اللغات

## التقنيات المستخدمة

### Frontend
- **HTML5**: هيكل الصفحات
- **CSS3**: التصميم والتنسيق مع دعم RTL
- **JavaScript (Vanilla)**: المنطق والتفاعل
- **Font Awesome**: الأيقونات

### المكتبات الخارجية
- **SheetJS (XLSX)**: تصدير ملفات Excel
- **jsPDF**: تصدير ملفات PDF

### التخزين
- **localStorage**: تخزين البيانات محلياً في المتصفح

## بنية المشروع

```
├── index.html              # صفحة تسجيل الدخول
├── dashboard.html          # لوحة التحكم الرئيسية
├── styles/
│   ├── main.css           # الأنماط الرئيسية
│   └── responsive.css     # التصميم المتجاوب
├── js/
│   ├── app.js            # التطبيق الرئيسي
│   ├── auth.js           # نظام المصادقة
│   ├── storage.js        # إدارة التخزين
│   ├── language.js       # نظام اللغات
│   ├── search.js         # البحث الشامل
│   ├── export.js         # تصدير البيانات
│   ├── customers.js      # إدارة العملاء
│   ├── suppliers.js      # إدارة الموردين
│   └── telesales.js      # إدارة فريق المبيعات
└── assets/               # الملفات المساعدة
```

## كيفية الاستخدام

### 1. تشغيل النظام
1. افتح ملف `index.html` في متصفح حديث
2. استخدم أحد الحسابات التجريبية للدخول:
   - **مدير**: admin / admin123
   - **مشرف**: supervisor / super123
   - **مبيعات**: sales / sales123

### 2. التنقل في النظام
- استخدم القائمة الجانبية للتنقل بين الوحدات
- استخدم شريط البحث العلوي للبحث السريع
- انقر على الأزرار لتنفيذ الإجراءات المختلفة

### 3. إدارة البيانات
- **إضافة**: استخدم أزرار "إضافة جديد" في كل وحدة
- **تعديل**: انقر على أيقونة التعديل في الجداول
- **حذف**: انقر على أيقونة الحذف (مع تأكيد)
- **عرض**: انقر مرتين على أي صف في الجدول

### 4. البحث والفلترة
- استخدم مربع البحث للبحث النصي
- استخدم القوائم المنسدلة للفلترة
- انقر على "مسح الفلاتر" لإعادة تعيين البحث

### 5. التصدير
- انقر على زر "تصدير" في أي وحدة
- اختر نوع البيانات والصيغة المطلوبة
- انقر على "تصدير" لتحميل الملف

## المميزات التقنية

### الأمان
- نظام مصادقة آمن
- إدارة الجلسات مع انتهاء صلاحية
- تسجيل أنشطة المستخدمين
- صلاحيات محددة لكل دور

### الأداء
- تحميل سريع للبيانات
- بحث فوري مع تأخير ذكي
- تقسيم الصفحات للجداول الكبيرة
- تحديث تلقائي للإحصائيات

### سهولة الاستخدام
- واجهة بديهية وحديثة
- رسائل تأكيد وتنبيه واضحة
- اختصارات لوحة المفاتيح
- تصميم متجاوب لجميع الأجهزة

### إمكانية الوصول
- دعم قارئات الشاشة
- تباين عالي للألوان
- تنقل بلوحة المفاتيح
- نصوص بديلة للصور

## البيانات التجريبية

يتضمن النظام بيانات تجريبية للاختبار:
- 5 عملاء نموذجيين
- 3 موردين
- 3 موظفي مبيعات
- 4 مهام متنوعة

## المتطلبات

### متصفح الويب
- Chrome 80+ (مُوصى به)
- Firefox 75+
- Safari 13+
- Edge 80+

### دعم JavaScript
- يجب تفعيل JavaScript
- دعم localStorage
- دعم ES6+

## الدعم والصيانة

### النسخ الاحتياطية
- نسخ احتياطية تلقائية للبيانات
- إمكانية تصدير واستيراد البيانات
- حفظ إعدادات المستخدم

### استكشاف الأخطاء
- رسائل خطأ واضحة ومفيدة
- تسجيل الأخطاء في وحدة التحكم
- إعادة تحميل تلقائية عند الحاجة

## التطوير المستقبلي

### مميزات مخططة
- تكامل مع قواعد البيانات الخارجية
- إشعارات فورية
- تقارير متقدمة مع رسوم بيانية
- تطبيق جوال مصاحب
- تكامل مع أنظمة CRM أخرى

### تحسينات تقنية
- تحسين الأداء
- دعم المزيد من صيغ التصدير
- واجهة برمجة تطبيقات (API)
- نظام إدارة المحتوى

## الترخيص

هذا المشروع مطور لأغراض تعليمية وتجريبية. يمكن استخدامه وتعديله حسب الحاجة.

## التواصل

لأي استفسارات أو اقتراحات، يرجى التواصل عبر:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-11-1234567

---

**تم تطوير هذا النظام باستخدام أحدث تقنيات الويب مع التركيز على سهولة الاستخدام والأداء العالي.**
