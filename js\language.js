// Language Support System for CRM
class LanguageManager {
    constructor() {
        this.currentLanguage = 'ar';
        this.translations = {
            ar: {
                // App Title and General
                app_title: 'نظام إدارة علاقات العملاء',
                english: 'English',
                arabic: 'العربية',
                
                // Login Page
                username: 'اسم المستخدم',
                password: 'كلمة المرور',
                role: 'الدور',
                select_role: 'اختر الدور',
                manager: 'مدير',
                supervisor: 'مشرف',
                telesales: 'مبيعات هاتفية',
                login: 'تسجيل الدخول',
                demo_accounts: 'حسابات تجريبية:',
                loading: 'جاري التحميل...',
                
                // Dashboard
                dashboard: 'لوحة التحكم',
                customers: 'العملاء',
                suppliers: 'الموردين',
                telesales_team: 'فريق المبيعات',
                reports: 'التقارير',
                system_settings: 'إعدادات النظام',
                settings: 'الإعدادات',
                logout: 'تسجيل الخروج',
                
                // Search
                global_search: 'البحث في جميع البيانات...',
                search_customers: 'البحث في العملاء...',
                search_suppliers: 'البحث في الموردين...',
                search_telesales: 'البحث في فريق المبيعات...',
                
                // Stats
                total_customers: 'إجمالي العملاء',
                total_suppliers: 'إجمالي الموردين',
                total_telesales: 'فريق المبيعات',
                active_tasks: 'المهام النشطة',
                recent_customers: 'العملاء الجدد',
                pending_tasks: 'المهام المعلقة',
                performance_overview: 'نظرة عامة على الأداء',
                
                // Customer Management
                customer_management: 'إدارة العملاء',
                add_customer: 'إضافة عميل جديد',
                edit_customer: 'تعديل العميل',
                delete_customer: 'حذف العميل',
                customer_name: 'اسم العميل',
                full_name: 'الاسم الكامل',
                mobile_phone: 'رقم الجوال',
                email_address: 'البريد الإلكتروني',
                city_location: 'المدينة/الموقع',
                business_type: 'نوع النشاط',
                industry: 'الصناعة',
                last_contact_date: 'تاريخ آخر تواصل',
                notes_comments: 'الملاحظات/التعليقات',
                customer_status: 'حالة العميل',
                active: 'نشط',
                inactive: 'غير نشط',
                prospect: 'محتمل',
                
                // Supplier Management
                supplier_management: 'إدارة الموردين',
                add_supplier: 'إضافة مورد جديد',
                edit_supplier: 'تعديل المورد',
                delete_supplier: 'حذف المورد',
                supplier_name: 'اسم المورد',
                service_type: 'نوع الخدمة',
                service_category: 'فئة الخدمة',
                contact_method: 'وسيلة التواصل',
                address: 'العنوان',
                phone: 'الهاتف',
                email: 'البريد الإلكتروني',
                
                // Telesales Management
                telesales_management: 'إدارة فريق المبيعات',
                add_employee: 'إضافة موظف جديد',
                edit_employee: 'تعديل الموظف',
                delete_employee: 'حذف الموظف',
                employee_name: 'اسم الموظف',
                employee_id: 'رقم الموظف',
                position: 'المنصب',
                hire_date: 'تاريخ التوظيف',
                department: 'القسم',
                daily_tasks: 'المهام اليومية',
                weekly_performance: 'الأداء الأسبوعي',
                assign_task: 'تخصيص مهمة',
                task_title: 'عنوان المهمة',
                task_description: 'وصف المهمة',
                task_priority: 'أولوية المهمة',
                task_deadline: 'موعد التسليم',
                task_status: 'حالة المهمة',
                pending: 'معلق',
                in_progress: 'قيد التنفيذ',
                completed: 'مكتمل',
                high: 'عالية',
                medium: 'متوسطة',
                low: 'منخفضة',
                
                // Actions
                save: 'حفظ',
                cancel: 'إلغاء',
                edit: 'تعديل',
                delete: 'حذف',
                view: 'عرض',
                add: 'إضافة',
                search: 'بحث',
                filter: 'تصفية',
                export: 'تصدير',
                import: 'استيراد',
                backup: 'نسخ احتياطي',
                restore: 'استعادة',
                print: 'طباعة',
                
                // Export
                export_excel: 'تصدير إلى Excel',
                export_pdf: 'تصدير إلى PDF',
                export_all_data: 'تصدير جميع البيانات',
                export_customers: 'تصدير العملاء',
                export_suppliers: 'تصدير الموردين',
                export_telesales: 'تصدير فريق المبيعات',
                
                // Messages
                success: 'نجح',
                error: 'خطأ',
                warning: 'تحذير',
                info: 'معلومات',
                confirm_delete: 'هل أنت متأكد من الحذف؟',
                data_saved: 'تم حفظ البيانات بنجاح',
                data_deleted: 'تم حذف البيانات بنجاح',
                data_exported: 'تم تصدير البيانات بنجاح',
                invalid_data: 'البيانات غير صحيحة',
                required_field: 'هذا الحقل مطلوب',
                
                // Validation
                invalid_email: 'البريد الإلكتروني غير صحيح',
                invalid_phone: 'رقم الهاتف غير صحيح',
                password_too_short: 'كلمة المرور قصيرة جداً',
                passwords_not_match: 'كلمات المرور غير متطابقة',
                
                // Date and Time
                today: 'اليوم',
                yesterday: 'أمس',
                this_week: 'هذا الأسبوع',
                this_month: 'هذا الشهر',
                last_month: 'الشهر الماضي',
                
                // Pagination
                previous: 'السابق',
                next: 'التالي',
                page: 'صفحة',
                of: 'من',
                showing: 'عرض',
                entries: 'إدخالات',
                
                // No Data
                no_data: 'لا توجد بيانات',
                no_customers: 'لا يوجد عملاء',
                no_suppliers: 'لا يوجد موردين',
                no_employees: 'لا يوجد موظفين',
                no_tasks: 'لا توجد مهام',
                no_results: 'لا توجد نتائج'
            },
            en: {
                // App Title and General
                app_title: 'Customer Relationship Management System',
                english: 'English',
                arabic: 'العربية',
                
                // Login Page
                username: 'Username',
                password: 'Password',
                role: 'Role',
                select_role: 'Select Role',
                manager: 'Manager',
                supervisor: 'Supervisor',
                telesales: 'Telesales',
                login: 'Login',
                demo_accounts: 'Demo Accounts:',
                loading: 'Loading...',
                
                // Dashboard
                dashboard: 'Dashboard',
                customers: 'Customers',
                suppliers: 'Suppliers',
                telesales_team: 'Telesales Team',
                reports: 'Reports',
                system_settings: 'System Settings',
                settings: 'Settings',
                logout: 'Logout',
                
                // Search
                global_search: 'Search all data...',
                search_customers: 'Search customers...',
                search_suppliers: 'Search suppliers...',
                search_telesales: 'Search telesales team...',
                
                // Stats
                total_customers: 'Total Customers',
                total_suppliers: 'Total Suppliers',
                total_telesales: 'Telesales Team',
                active_tasks: 'Active Tasks',
                recent_customers: 'Recent Customers',
                pending_tasks: 'Pending Tasks',
                performance_overview: 'Performance Overview',
                
                // Customer Management
                customer_management: 'Customer Management',
                add_customer: 'Add New Customer',
                edit_customer: 'Edit Customer',
                delete_customer: 'Delete Customer',
                customer_name: 'Customer Name',
                full_name: 'Full Name',
                mobile_phone: 'Mobile Phone',
                email_address: 'Email Address',
                city_location: 'City/Location',
                business_type: 'Business Type',
                industry: 'Industry',
                last_contact_date: 'Last Contact Date',
                notes_comments: 'Notes/Comments',
                customer_status: 'Customer Status',
                active: 'Active',
                inactive: 'Inactive',
                prospect: 'Prospect',
                
                // Supplier Management
                supplier_management: 'Supplier Management',
                add_supplier: 'Add New Supplier',
                edit_supplier: 'Edit Supplier',
                delete_supplier: 'Delete Supplier',
                supplier_name: 'Supplier Name',
                service_type: 'Service Type',
                service_category: 'Service Category',
                contact_method: 'Contact Method',
                address: 'Address',
                phone: 'Phone',
                email: 'Email',
                
                // Telesales Management
                telesales_management: 'Telesales Team Management',
                add_employee: 'Add New Employee',
                edit_employee: 'Edit Employee',
                delete_employee: 'Delete Employee',
                employee_name: 'Employee Name',
                employee_id: 'Employee ID',
                position: 'Position',
                hire_date: 'Hire Date',
                department: 'Department',
                daily_tasks: 'Daily Tasks',
                weekly_performance: 'Weekly Performance',
                assign_task: 'Assign Task',
                task_title: 'Task Title',
                task_description: 'Task Description',
                task_priority: 'Task Priority',
                task_deadline: 'Task Deadline',
                task_status: 'Task Status',
                pending: 'Pending',
                in_progress: 'In Progress',
                completed: 'Completed',
                high: 'High',
                medium: 'Medium',
                low: 'Low',
                
                // Actions
                save: 'Save',
                cancel: 'Cancel',
                edit: 'Edit',
                delete: 'Delete',
                view: 'View',
                add: 'Add',
                search: 'Search',
                filter: 'Filter',
                export: 'Export',
                import: 'Import',
                backup: 'Backup',
                restore: 'Restore',
                print: 'Print',
                
                // Export
                export_excel: 'Export to Excel',
                export_pdf: 'Export to PDF',
                export_all_data: 'Export All Data',
                export_customers: 'Export Customers',
                export_suppliers: 'Export Suppliers',
                export_telesales: 'Export Telesales Team',
                
                // Messages
                success: 'Success',
                error: 'Error',
                warning: 'Warning',
                info: 'Information',
                confirm_delete: 'Are you sure you want to delete?',
                data_saved: 'Data saved successfully',
                data_deleted: 'Data deleted successfully',
                data_exported: 'Data exported successfully',
                invalid_data: 'Invalid data',
                required_field: 'This field is required',
                
                // Validation
                invalid_email: 'Invalid email address',
                invalid_phone: 'Invalid phone number',
                password_too_short: 'Password is too short',
                passwords_not_match: 'Passwords do not match',
                
                // Date and Time
                today: 'Today',
                yesterday: 'Yesterday',
                this_week: 'This Week',
                this_month: 'This Month',
                last_month: 'Last Month',
                
                // Pagination
                previous: 'Previous',
                next: 'Next',
                page: 'Page',
                of: 'of',
                showing: 'Showing',
                entries: 'entries',
                
                // No Data
                no_data: 'No data available',
                no_customers: 'No customers found',
                no_suppliers: 'No suppliers found',
                no_employees: 'No employees found',
                no_tasks: 'No tasks found',
                no_results: 'No results found'
            }
        };
        
        this.init();
    }
    
    init() {
        // Load saved language preference
        const savedLanguage = localStorage.getItem('crm_language');
        if (savedLanguage && this.translations[savedLanguage]) {
            this.currentLanguage = savedLanguage;
        }
        
        this.updateLanguage();
        this.bindEvents();
    }
    
    bindEvents() {
        // Language toggle button
        document.addEventListener('click', (e) => {
            if (e.target.closest('#languageToggle')) {
                this.toggleLanguage();
            }
        });
    }
    
    toggleLanguage() {
        this.currentLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';
        this.updateLanguage();
        this.saveLanguagePreference();
    }
    
    updateLanguage() {
        const html = document.documentElement;
        const translations = this.translations[this.currentLanguage];
        
        // Update HTML attributes
        html.lang = this.currentLanguage;
        html.dir = this.currentLanguage === 'ar' ? 'rtl' : 'ltr';
        
        // Update all elements with data-key attributes
        document.querySelectorAll('[data-key]').forEach(element => {
            const key = element.getAttribute('data-key');
            if (translations[key]) {
                element.textContent = translations[key];
            }
        });
        
        // Update placeholder attributes
        document.querySelectorAll('[data-key-placeholder]').forEach(element => {
            const key = element.getAttribute('data-key-placeholder');
            if (translations[key]) {
                element.placeholder = translations[key];
            }
        });
        
        // Update language toggle button text
        const languageToggle = document.querySelector('#languageToggle span');
        if (languageToggle) {
            languageToggle.textContent = this.currentLanguage === 'ar' ? 'English' : 'العربية';
        }
        
        // Trigger custom event for other components
        document.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: this.currentLanguage, translations }
        }));
    }
    
    saveLanguagePreference() {
        localStorage.setItem('crm_language', this.currentLanguage);
    }
    
    getCurrentLanguage() {
        return this.currentLanguage;
    }
    
    getTranslation(key) {
        return this.translations[this.currentLanguage][key] || key;
    }
    
    getAllTranslations() {
        return this.translations[this.currentLanguage];
    }
    
    isRTL() {
        return this.currentLanguage === 'ar';
    }
}

// Global language manager instance
let languageManager;

// Initialize language system
function initializeLanguage() {
    languageManager = new LanguageManager();
    return languageManager;
}

// Helper functions for global access
function getCurrentLanguage() {
    return languageManager ? languageManager.getCurrentLanguage() : 'ar';
}

function getTranslation(key) {
    return languageManager ? languageManager.getTranslation(key) : key;
}

function isRTL() {
    return languageManager ? languageManager.isRTL() : true;
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LanguageManager, initializeLanguage, getCurrentLanguage, getTranslation, isRTL };
}
